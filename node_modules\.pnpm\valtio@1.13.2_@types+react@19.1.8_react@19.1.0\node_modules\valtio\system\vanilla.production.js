System.register(["proxy-compare"],function(K){"use strict";var A,T;return{setters:[function(j){A=j.markToTrack,T=j.getUntracked}],execute:function(){K({getVersion:U,proxy:L,ref:B,snapshot:q,subscribe:_});const j=s=>typeof s=="object"&&s!==null,f=new WeakMap,k=new WeakSet,V=(s=Object.is,l=(e,w)=>new Proxy(e,w),O=e=>j(e)&&!k.has(e)&&(Array.isArray(e)||!(Symbol.iterator in e))&&!(e instanceof WeakMap)&&!(e instanceof WeakSet)&&!(e instanceof Error)&&!(e instanceof Number)&&!(e instanceof Date)&&!(e instanceof String)&&!(e instanceof RegExp)&&!(e instanceof ArrayBuffer),P=e=>{switch(e.status){case"fulfilled":return e.value;case"rejected":throw e.reason;default:throw e}},u=new WeakMap,d=(e,w,y=P)=>{const a=u.get(e);if((a==null?void 0:a[0])===w)return a[1];const i=Array.isArray(e)?[]:Object.create(Object.getPrototypeOf(e));return A(i,!0),u.set(e,[w,i]),Reflect.ownKeys(e).forEach(v=>{if(Object.getOwnPropertyDescriptor(i,v))return;const g=Reflect.get(e,v),{enumerable:E}=Reflect.getOwnPropertyDescriptor(e,v),c={value:g,enumerable:E,configurable:!0};if(k.has(g))A(g,!1);else if(g instanceof Promise)delete c.value,c.get=()=>y(g);else if(f.has(g)){const[M,x]=f.get(g);c.value=d(M,x(),y)}Object.defineProperty(i,v,c)}),Object.preventExtensions(i)},h=new WeakMap,b=[1,1],R=e=>{if(!j(e))throw new Error("object required");const w=h.get(e);if(w)return w;let y=b[0];const a=new Set,i=(n,t=++b[0])=>{y!==t&&(y=t,a.forEach(r=>r(n,t)))};let v=b[1];const g=(n=++b[1])=>(v!==n&&!a.size&&(v=n,c.forEach(([t])=>{const r=t[1](n);r>y&&(y=r)})),y),E=n=>(t,r)=>{const o=[...t];o[1]=[n,...o[1]],i(o,r)},c=new Map,M=(n,t)=>{if(a.size){const r=t[3](E(n));c.set(n,[t,r])}else c.set(n,[t])},x=n=>{var t;const r=c.get(n);r&&(c.delete(n),(t=r[1])==null||t.call(r))},N=n=>(a.add(n),a.size===1&&c.forEach(([t,r],o)=>{const z=t[3](E(o));c.set(o,[t,z])}),()=>{a.delete(n),a.size===0&&c.forEach(([t,r],o)=>{r&&(r(),c.set(o,[t]))})}),S=Array.isArray(e)?[]:Object.create(Object.getPrototypeOf(e)),m=l(S,{deleteProperty(n,t){const r=Reflect.get(n,t);x(t);const o=Reflect.deleteProperty(n,t);return o&&i(["delete",[t],r]),o},set(n,t,r,o){const z=Reflect.has(n,t),D=Reflect.get(n,t,o);if(z&&(s(D,r)||h.has(r)&&s(D,h.get(r))))return!0;x(t),j(r)&&(r=T(r)||r);let W=r;if(r instanceof Promise)r.then(p=>{r.status="fulfilled",r.value=p,i(["resolve",[t],p])}).catch(p=>{r.status="rejected",r.reason=p,i(["reject",[t],p])});else{!f.has(r)&&O(r)&&(W=R(r));const p=!k.has(W)&&f.get(W);p&&M(t,p)}return Reflect.set(n,t,W,o),i(["set",[t],r,D]),!0}});h.set(e,m);const C=[S,g,d,N];return f.set(m,C),Reflect.ownKeys(e).forEach(n=>{const t=Object.getOwnPropertyDescriptor(e,n);"value"in t&&(m[n]=e[n],delete t.value,delete t.writable),Object.defineProperty(S,n,t)}),m})=>[R,f,k,s,l,O,P,u,d,h,b],[F]=V();function L(s={}){return F(s)}function U(s){const l=f.get(s);return l==null?void 0:l[1]()}function _(s,l,O){const P=f.get(s);let u;const d=[],h=P[3];let b=!1;const R=h(e=>{if(d.push(e),O){l(d.splice(0));return}u||(u=Promise.resolve().then(()=>{u=void 0,b&&l(d.splice(0))}))});return b=!0,()=>{b=!1,R()}}function q(s,l){const O=f.get(s),[P,u,d]=O;return d(P,u(),l)}function B(s){return k.add(s),s}const G=K("unstable_buildProxyFunction",V)}}});
