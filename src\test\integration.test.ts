import { describe, it, expect, beforeEach, afterEach } from 'vitest'
import { createSharedState } from '../index'
import type { SharedStateManager } from '../core'

interface TodoState {
  todos: Array<{
    id: number
    text: string
    completed: boolean
    createdBy: string
  }>
  filter: 'all' | 'active' | 'completed'
  user: {
    name: string
    preferences: {
      theme: 'light' | 'dark'
    }
  }
}

describe('Integration Tests', () => {
  let manager1: SharedStateManager<TodoState>
  let manager2: SharedStateManager<TodoState>

  const initialState: TodoState = {
    todos: [],
    filter: 'all',
    user: {
      name: 'Anonymous',
      preferences: {
        theme: 'light',
      },
    },
  }

  beforeEach(() => {
    localStorage.clear()

    manager1 = createSharedState(initialState, {
      storageKey: 'todo-integration-test',
      channelName: 'todo-test-channel',
      debug: false,
    })

    manager2 = createSharedState(initialState, {
      storageKey: 'todo-integration-test',
      channelName: 'todo-test-channel',
      debug: false,
    })
  })

  afterEach(() => {
    manager1.destroy()
    manager2.destroy()
  })

  it('should create shared state with createSharedState function', () => {
    expect(manager1).toBeDefined()
    expect(manager1.getState()).toEqual(initialState)
    expect(manager1.getTabId()).toBeTruthy()
  })

  it('should sync state between two managers', async () => {
    // Wait for managers to discover each other
    await new Promise<void>((resolve) => {
      manager1.on('connected', () => resolve())
      manager1.forceSync()
    })

    expect(manager1.isConnected()).toBe(true)
    expect(manager2.isConnected()).toBe(true)

    // Add todo in manager1
    const state1 = manager1.getState()
    state1.todos.push({
      id: 1,
      text: 'Test todo',
      completed: false,
      createdBy: manager1.getTabId(),
    })

    // Wait for sync
    await new Promise<void>((resolve) => {
      manager2.on('sync', () => resolve())
    })

    // Check that manager2 received the update
    const state2 = manager2.getState()
    expect(state2.todos).toHaveLength(1)
    expect(state2.todos[0].text).toBe('Test todo')
    expect(state2.todos[0].createdBy).toBe(manager1.getTabId())
  })

  it('should handle complex state mutations', async () => {
    // Establish connection
    await new Promise<void>((resolve) => {
      manager1.on('connected', () => resolve())
      manager1.forceSync()
    })

    const state1 = manager1.getState()
    const state2 = manager2.getState()

    // Complex mutations in manager1
    state1.user.name = 'John Doe'
    state1.user.preferences.theme = 'dark'
    state1.filter = 'active'
    state1.todos.push(
      {
        id: 1,
        text: 'First todo',
        completed: false,
        createdBy: manager1.getTabId(),
      },
      {
        id: 2,
        text: 'Second todo',
        completed: true,
        createdBy: manager1.getTabId(),
      }
    )

    // Wait for sync
    await new Promise<void>((resolve) => {
      manager2.on('sync', () => resolve())
    })

    // Verify all changes synced
    expect(state2.user.name).toBe('John Doe')
    expect(state2.user.preferences.theme).toBe('dark')
    expect(state2.filter).toBe('active')
    expect(state2.todos).toHaveLength(2)
    expect(state2.todos[0].text).toBe('First todo')
    expect(state2.todos[1].completed).toBe(true)
  })

  it('should handle bidirectional sync', async () => {
    // Establish connection
    await new Promise<void>((resolve) => {
      manager1.on('connected', () => resolve())
      manager1.forceSync()
    })

    const state1 = manager1.getState()
    const state2 = manager2.getState()

    // Add todo from manager1
    state1.todos.push({
      id: 1,
      text: 'From manager1',
      completed: false,
      createdBy: manager1.getTabId(),
    })

    // Wait for sync to manager2
    await new Promise<void>((resolve) => {
      manager2.on('sync', () => resolve())
    })

    expect(state2.todos).toHaveLength(1)

    // Add todo from manager2
    state2.todos.push({
      id: 2,
      text: 'From manager2',
      completed: false,
      createdBy: manager2.getTabId(),
    })

    // Wait for sync back to manager1
    await new Promise<void>((resolve) => {
      manager1.on('sync', () => resolve())
    })

    // Both managers should have both todos
    expect(state1.todos).toHaveLength(2)
    expect(state2.todos).toHaveLength(2)

    const texts = state1.todos.map((t) => t.text).sort()
    expect(texts).toEqual(['From manager1', 'From manager2'])
  })

  it('should persist state across manager recreation', () => {
    const state = manager1.getState()
    state.user.name = 'Persistent User'
    state.todos.push({
      id: 1,
      text: 'Persistent todo',
      completed: false,
      createdBy: manager1.getTabId(),
    })

    // Wait for persistence
    setTimeout(() => {
      manager1.destroy()

      // Create new manager with same storage key
      const newManager = createSharedState(initialState, {
        storageKey: 'todo-integration-test',
        channelName: 'todo-test-channel-new',
      })

      const newState = newManager.getState()
      expect(newState.user.name).toBe('Persistent User')
      expect(newState.todos).toHaveLength(1)
      expect(newState.todos[0].text).toBe('Persistent todo')

      newManager.destroy()
    }, 50)
  })

  it('should handle conflict resolution', async () => {
    // Create managers with different conflict resolution strategies
    const mergeManager1 = createSharedState(initialState, {
      storageKey: 'merge-test',
      channelName: 'merge-channel',
      conflictResolution: 'merge-deep',
    })

    const mergeManager2 = createSharedState(initialState, {
      storageKey: 'merge-test',
      channelName: 'merge-channel',
      conflictResolution: 'merge-deep',
    })

    // Establish connection
    await new Promise<void>((resolve) => {
      mergeManager1.on('connected', () => resolve())
      mergeManager1.forceSync()
    })

    const state1 = mergeManager1.getState()
    const state2 = mergeManager2.getState()

    // Make different changes
    state1.user.name = 'User from Tab 1'
    state1.filter = 'active'

    state2.user.preferences.theme = 'dark'
    state2.todos.push({
      id: 1,
      text: 'Todo from Tab 2',
      completed: false,
      createdBy: mergeManager2.getTabId(),
    })

    // Force sync from both sides
    mergeManager1.forceSync()
    mergeManager2.forceSync()

    // Wait for resolution
    await new Promise((resolve) => setTimeout(resolve, 100))

    // Both should have merged state
    expect(state1.user.name).toBe('User from Tab 1')
    expect(state1.user.preferences.theme).toBe('dark')
    expect(state1.filter).toBe('active')
    expect(state1.todos).toHaveLength(1)

    mergeManager1.destroy()
    mergeManager2.destroy()
  })

  it('should handle array operations correctly', async () => {
    // Establish connection
    await new Promise<void>((resolve) => {
      manager1.on('connected', () => resolve())
      manager1.forceSync()
    })

    const state1 = manager1.getState()
    const state2 = manager2.getState()

    // Add multiple todos
    state1.todos.push(
      { id: 1, text: 'Todo 1', completed: false, createdBy: manager1.getTabId() },
      { id: 2, text: 'Todo 2', completed: false, createdBy: manager1.getTabId() },
      { id: 3, text: 'Todo 3', completed: false, createdBy: manager1.getTabId() }
    )

    await new Promise<void>((resolve) => {
      manager2.on('sync', () => resolve())
    })

    expect(state2.todos).toHaveLength(3)

    // Remove middle todo
    state1.todos.splice(1, 1)

    await new Promise<void>((resolve) => {
      manager2.on('sync', () => resolve())
    })

    expect(state2.todos).toHaveLength(2)
    expect(state2.todos[0].text).toBe('Todo 1')
    expect(state2.todos[1].text).toBe('Todo 3')

    // Mark todo as completed
    state2.todos[0].completed = true

    await new Promise<void>((resolve) => {
      manager1.on('sync', () => resolve())
    })

    expect(state1.todos[0].completed).toBe(true)
  })
})
