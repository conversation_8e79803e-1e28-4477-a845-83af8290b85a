import type { SharedStateOptions, SerializableState } from './types'
import { SharedStateManager } from './core'

/**
 * Create a shared state that synchronizes across browser tabs
 *
 * @param initialState - The initial state object
 * @param options - Configuration options
 * @returns SharedStateManager instance
 *
 * @example
 * ```typescript
 * const todoState = createSharedState({
 *   todos: [],
 *   filter: 'all'
 * }, {
 *   storageKey: 'todo-app',
 *   debug: true
 * })
 *
 * // In React component
 * function TodoApp() {
 *   const { state, isConnected, tabId } = useSharedState(todoState)
 *
 *   const addTodo = (text: string) => {
 *     state.todos.push({ id: Date.now(), text, completed: false })
 *   }
 *
 *   return (
 *     <div>
 *       <div>Connected: {isConnected ? '✅' : '❌'}</div>
 *       <div>Tab: {tabId}</div>
 *       {state.todos.map(todo => (
 *         <div key={todo.id}>{todo.text}</div>
 *       ))}
 *     </div>
 *   )
 * }
 * ```
 */
export function createSharedState<T extends SerializableState>(
  initialState: T,
  options?: SharedStateOptions
): SharedStateManager<T> {
  return new SharedStateManager(initialState, options)
}

// Re-export everything
export * from './types'
export * from './core'
export * from './hook'
export * from './utils'
