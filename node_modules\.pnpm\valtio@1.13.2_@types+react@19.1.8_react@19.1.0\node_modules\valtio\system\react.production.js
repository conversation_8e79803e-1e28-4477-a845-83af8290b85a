System.register(["react","proxy-compare","use-sync-external-store/shim","valtio/vanilla"],function(v){"use strict";var s,a,o,f,i,p,l,h,y,c;return{setters:[function(e){s=e.useRef,a=e.useCallback,o=e.useEffect,f=e.useMemo,i=e.default},function(e){p=e.isChanged,l=e.createProxy},function(e){h=e.default},function(e){y=e.subscribe,c=e.snapshot}],execute:function(){v("useSnapshot",g);const{use:e}=i,{useSyncExternalStore:C}=h,W=new WeakMap;function g(t,b){const k=b==null?void 0:b.sync,r=s(),u=s();let x=!0;const M=C(a(n=>{const w=y(t,n,k);return n(),w},[t,k]),()=>{const n=c(t,e);try{if(!x&&r.current&&u.current&&!p(r.current,n,u.current,new WeakMap))return r.current}catch(w){}return n},()=>c(t,e));x=!1;const S=new WeakMap;o(()=>{r.current=M,u.current=S});const E=f(()=>new WeakMap,[]);return l(M,S,E,W)}}}});
