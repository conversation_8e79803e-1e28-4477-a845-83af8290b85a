import { vi } from 'vitest'

// Mock BroadcastChannel for testing
class MockBroadcastChannel {
  name: string
  onmessage: ((event: MessageEvent) => void) | null = null
  
  static channels: Map<string, MockBroadcastChannel[]> = new Map()
  
  constructor(name: string) {
    this.name = name
    
    if (!MockBroadcastChannel.channels.has(name)) {
      MockBroadcastChannel.channels.set(name, [])
    }
    
    MockBroadcastChannel.channels.get(name)!.push(this)
  }
  
  postMessage(data: any) {
    const channels = MockBroadcastChannel.channels.get(this.name) || []
    
    // Simulate async message delivery
    setTimeout(() => {
      channels.forEach(channel => {
        if (channel !== this && channel.onmessage) {
          channel.onmessage(new MessageEvent('message', { data }))
        }
      })
    }, 0)
  }
  
  close() {
    const channels = MockBroadcastChannel.channels.get(this.name) || []
    const index = channels.indexOf(this)
    if (index > -1) {
      channels.splice(index, 1)
    }
  }
  
  addEventListener(type: string, listener: (event: MessageEvent) => void) {
    if (type === 'message') {
      this.onmessage = listener
    }
  }
  
  removeEventListener(type: string, listener: (event: MessageEvent) => void) {
    if (type === 'message' && this.onmessage === listener) {
      this.onmessage = null
    }
  }
  
  static clearAll() {
    this.channels.clear()
  }
}

// Mock localStorage
const localStorageMock = {
  store: new Map<string, string>(),
  
  getItem(key: string): string | null {
    return this.store.get(key) || null
  },
  
  setItem(key: string, value: string): void {
    this.store.set(key, value)
  },
  
  removeItem(key: string): void {
    this.store.delete(key)
  },
  
  clear(): void {
    this.store.clear()
  },
  
  get length(): number {
    return this.store.size
  },
  
  key(index: number): string | null {
    const keys = Array.from(this.store.keys())
    return keys[index] || null
  }
}

// Setup global mocks
global.BroadcastChannel = MockBroadcastChannel as any
global.localStorage = localStorageMock as any

// Mock crypto.randomUUID
Object.defineProperty(global, 'crypto', {
  value: {
    randomUUID: vi.fn(() => 'test-uuid-' + Math.random().toString(36).substr(2, 9))
  }
})

// Clean up after each test
afterEach(() => {
  MockBroadcastChannel.clearAll()
  localStorageMock.clear()
  vi.clearAllMocks()
})
