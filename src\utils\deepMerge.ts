import { merge, cloneDeep, isEqual } from 'lodash'

/**
 * Deep merge two objects using lodash
 * Arrays are replaced, not merged (lodash default behavior)
 */
export function deepMerge<T extends Record<string, any>>(
  target: T,
  source: Partial<T>
): T {
  return merge({}, target, source)
}

/**
 * Shallow merge two objects
 */
export function shallowMerge<T extends Record<string, any>>(
  target: T,
  source: Partial<T>
): T {
  return { ...target, ...source }
}

/**
 * Deep clone an object using lodash
 */
export function deepClone<T>(obj: T): T {
  return cloneDeep(obj)
}

/**
 * Check if two objects are deeply equal using lodash
 */
export function deepEqual(a: any, b: any): boolean {
  return isEqual(a, b)
}
