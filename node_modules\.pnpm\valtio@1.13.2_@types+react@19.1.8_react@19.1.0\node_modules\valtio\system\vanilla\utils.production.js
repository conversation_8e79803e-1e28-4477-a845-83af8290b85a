System.register(["valtio/vanilla","derive-valtio"],function(g){"use strict";var b,p,y,m,w,T;return{setters:[function(c){b=c.subscribe,p=c.snapshot,y=c.proxy,m=c.ref,w=c.unstable_buildProxyFunction},function(c){T=c.derive,g({derive:c.derive,underive:c.underive,unstable_deriveSubscriptions:c.unstable_deriveSubscriptions})}],execute:function(){g({addComputed:M,devtools:N,proxyMap:z,proxySet:R,proxyWithComputed:I,proxyWithHistory:H,subscribeKey:c,watch:D});function c(a,s,t,e){let n=a[s];return b(a,()=>{const o=a[s];Object.is(n,o)||t(n=o)},e)}let v;function D(a,s){let t=!0;const e=new Set,n=new Map,o=()=>{t&&(t=!1,e.forEach(i=>i()),e.clear(),n.forEach(i=>i()),n.clear())},l=async()=>{if(!t)return;e.forEach(d=>d()),e.clear();const i=new Set,O=v;v=e;try{const d=a(u=>{if(i.add(u),t&&!n.has(u)){const h=b(u,l,s==null?void 0:s.sync);n.set(u,h)}return u}),r=d&&d instanceof Promise?await d:d;r&&(t?e.add(r):o())}finally{v=O}n.forEach((d,r)=>{i.has(r)||(n.delete(r),d())})};return v&&v.add(o),l(),o}const E=Symbol();function N(a,s){typeof s=="string"&&(console.warn("string name option is deprecated, use { name }. https://github.com/pmndrs/valtio/pull/400"),s={name:s});const{enabled:t,name:e="",...n}=s||{};let o;try{o=(t!=null?t:!1)&&window.__REDUX_DEVTOOLS_EXTENSION__}catch(r){}if(!o)return;let l=!1;const i=o.connect({name:e,...n}),O=b(a,r=>{const u=r.filter(([h,f])=>f[0]!==E).map(([h,f])=>`${h}:${f.map(String).join(".")}`).join(", ");if(u)if(l)l=!1;else{const h=Object.assign({},p(a));delete h[E],i.send({type:u,updatedAt:new Date().toLocaleString()},h)}}),d=i.subscribe(r=>{var u,h,f,_,P,A;if(r.type==="ACTION"&&r.payload)try{Object.assign(a,JSON.parse(r.payload))}catch(S){console.error(`please dispatch a serializable value that JSON.parse() and proxy() support
`,S)}if(r.type==="DISPATCH"&&r.state){if(((u=r.payload)==null?void 0:u.type)==="JUMP_TO_ACTION"||((h=r.payload)==null?void 0:h.type)==="JUMP_TO_STATE"){l=!0;const S=JSON.parse(r.state);Object.assign(a,S)}a[E]=r}else if(r.type==="DISPATCH"&&((f=r.payload)==null?void 0:f.type)==="COMMIT")i.init(p(a));else if(r.type==="DISPATCH"&&((_=r.payload)==null?void 0:_.type)==="IMPORT_STATE"){const S=(P=r.payload.nextLiftedState)==null?void 0:P.actionsById,U=((A=r.payload.nextLiftedState)==null?void 0:A.computedStates)||[];l=!0,U.forEach(({state:k},C)=>{const L=S[C]||"No action found";Object.assign(a,k),C===0?i.init(p(a)):i.send(L,p(a))})}});return i.init(p(a)),()=>{O(),d==null||d()}}function M(a,s,t=a){const e={};return Object.keys(s).forEach(n=>{e[n]=o=>s[n](o(a))}),T(e,{proxy:t})}function I(a,s){Object.keys(s).forEach(e=>{if(Object.getOwnPropertyDescriptor(a,e))throw new Error("object property already defined");const n=s[e],{get:o,set:l}=typeof n=="function"?{get:n}:n,i={};i.get=()=>o(p(t)),l&&(i.set=O=>l(t,O)),Object.defineProperty(a,e,i)});const t=y(a);return t}const J=a=>typeof a=="object"&&a!==null;let x;const j=a=>{if(x||(x=w()[2]),!J(a)||x.has(a))return a;const s=Array.isArray(a)?[]:Object.create(Object.getPrototypeOf(a));return Reflect.ownKeys(a).forEach(t=>{s[t]=j(a[t])}),s};function H(a,s=!1){const t=y({value:a,history:m({wip:void 0,snapshots:[],index:-1}),clone:j,canUndo:()=>t.history.index>0,undo:()=>{t.canUndo()&&(t.value=t.history.wip=t.clone(t.history.snapshots[--t.history.index]))},canRedo:()=>t.history.index<t.history.snapshots.length-1,redo:()=>{t.canRedo()&&(t.value=t.history.wip=t.clone(t.history.snapshots[++t.history.index]))},saveHistory:()=>{t.history.snapshots.splice(t.history.index+1),t.history.snapshots.push(p(t).value),++t.history.index},subscribe:()=>b(t,e=>{e.every(n=>n[1][0]==="value"&&(n[0]!=="set"||n[2]!==t.history.wip))&&t.saveHistory()})});return t.saveHistory(),s||t.subscribe(),t}function R(a){const s=y({data:Array.from(new Set(a)),has(t){return this.data.indexOf(t)!==-1},add(t){let e=!1;return typeof t=="object"&&t!==null&&(e=this.data.indexOf(y(t))!==-1),this.data.indexOf(t)===-1&&!e&&this.data.push(t),this},delete(t){const e=this.data.indexOf(t);return e===-1?!1:(this.data.splice(e,1),!0)},clear(){this.data.splice(0)},get size(){return this.data.length},forEach(t){this.data.forEach(e=>{t(e,e,this)})},get[Symbol.toStringTag](){return"Set"},toJSON(){return new Set(this.data)},[Symbol.iterator](){return this.data[Symbol.iterator]()},values(){return this.data.values()},keys(){return this.data.values()},entries(){return new Set(this.data).entries()}});return Object.defineProperties(s,{data:{enumerable:!1},size:{enumerable:!1},toJSON:{enumerable:!1}}),Object.seal(s),s}function z(a){const s=y({data:Array.from(a||[]),has(t){return this.data.some(e=>e[0]===t)},set(t,e){const n=this.data.find(o=>o[0]===t);return n?n[1]=e:this.data.push([t,e]),this},get(t){var e;return(e=this.data.find(n=>n[0]===t))==null?void 0:e[1]},delete(t){const e=this.data.findIndex(n=>n[0]===t);return e===-1?!1:(this.data.splice(e,1),!0)},clear(){this.data.splice(0)},get size(){return this.data.length},toJSON(){return new Map(this.data)},forEach(t){this.data.forEach(e=>{t(e[1],e[0],this)})},keys(){return this.data.map(t=>t[0]).values()},values(){return this.data.map(t=>t[1]).values()},entries(){return new Map(this.data).entries()},get[Symbol.toStringTag](){return"Map"},[Symbol.iterator](){return this.entries()}});return Object.defineProperties(s,{data:{enumerable:!1},size:{enumerable:!1},toJSON:{enumerable:!1}}),Object.seal(s),s}}}});
