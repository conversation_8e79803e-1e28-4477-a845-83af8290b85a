import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { SharedStateManager } from '@/core/SharedStateManager'

interface TestState {
  count: number
  text: string
  nested?: {
    value: number
  }
}

describe('SharedStateManager', () => {
  let manager: SharedStateManager<TestState>
  const initialState: TestState = { count: 0, text: 'hello' }

  beforeEach(() => {
    localStorage.clear()
    manager = new SharedStateManager(initialState, {
      storageKey: 'test-state',
      debug: false
    })
  })

  afterEach(() => {
    manager.destroy()
  })

  it('should initialize with initial state', () => {
    const state = manager.getState()
    expect(state.count).toBe(0)
    expect(state.text).toBe('hello')
  })

  it('should provide snapshot of current state', () => {
    const snapshot = manager.getSnapshot()
    expect(snapshot).toEqual(initialState)
    
    // Snapshot should be immutable
    expect(() => {
      (snapshot as any).count = 999
    }).toThrow()
  })

  it('should have unique tab ID', () => {
    const tabId = manager.getTabId()
    expect(tabId).toBeTruthy()
    expect(typeof tabId).toBe('string')
  })

  it('should start disconnected', () => {
    expect(manager.isConnected()).toBe(false)
    
    const status = manager.getConnectionStatus()
    expect(status.isConnected).toBe(false)
    expect(status.connectedTabs).toBe(0)
    expect(status.lastSync).toBeNull()
    expect(status.errors).toEqual([])
  })

  it('should persist state to localStorage', () => {
    const state = manager.getState()
    state.count = 42
    state.text = 'updated'

    // Give some time for the subscription to trigger
    setTimeout(() => {
      const stored = localStorage.getItem('test-state')
      expect(stored).toBeTruthy()
      
      const parsed = JSON.parse(stored!)
      expect(parsed.data.count).toBe(42)
      expect(parsed.data.text).toBe('updated')
    }, 10)
  })

  it('should load persisted state on initialization', () => {
    // First, save some state
    const state = manager.getState()
    state.count = 100
    state.text = 'persisted'
    
    // Wait for persistence
    setTimeout(() => {
      manager.destroy()
      
      // Create new manager - should load persisted state
      const newManager = new SharedStateManager(initialState, {
        storageKey: 'test-state'
      })
      
      const loadedState = newManager.getState()
      expect(loadedState.count).toBe(100)
      expect(loadedState.text).toBe('persisted')
      
      newManager.destroy()
    }, 10)
  })

  it('should handle options correctly', () => {
    const customManager = new SharedStateManager(initialState, {
      storageKey: 'custom-key',
      persist: false,
      persistOnClose: false,
      channelName: 'custom-channel',
      conflictResolution: 'merge-deep',
      debug: true
    })

    expect(customManager).toBeDefined()
    customManager.destroy()
  })

  it('should force sync', () => {
    const spy = vi.spyOn(manager['syncEngine'], 'broadcastUpdate')
    
    manager.forceSync()
    
    expect(spy).toHaveBeenCalled()
    spy.mockRestore()
  })

  it('should clear storage', () => {
    // First save some data
    const state = manager.getState()
    state.count = 42
    
    setTimeout(() => {
      expect(localStorage.getItem('test-state')).toBeTruthy()
      
      manager.clearStorage()
      expect(localStorage.getItem('test-state')).toBeNull()
    }, 10)
  })

  it('should handle event listeners', () => {
    const listener = vi.fn()
    
    manager.on('connected', listener)
    manager.off('connected', listener)
    
    // Should not throw
    expect(() => {
      manager.on('sync', () => {})
      manager.off('sync', () => {})
    }).not.toThrow()
  })

  it('should handle state mutations reactively', async () => {
    const state = manager.getState()
    
    // Mutate state
    state.count = 999
    state.text = 'mutated'
    state.nested = { value: 123 }
    
    // Check that state is updated
    expect(state.count).toBe(999)
    expect(state.text).toBe('mutated')
    expect(state.nested?.value).toBe(123)
    
    // Check snapshot reflects changes
    const snapshot = manager.getSnapshot()
    expect(snapshot.count).toBe(999)
    expect(snapshot.text).toBe('mutated')
    expect(snapshot.nested?.value).toBe(123)
  })

  it('should handle nested object mutations', () => {
    const state = manager.getState()
    state.nested = { value: 1 }
    
    // Mutate nested object
    state.nested.value = 42
    
    expect(state.nested.value).toBe(42)
    
    const snapshot = manager.getSnapshot()
    expect(snapshot.nested?.value).toBe(42)
  })

  it('should handle array mutations', () => {
    interface StateWithArray {
      items: number[]
    }
    
    const arrayManager = new SharedStateManager<StateWithArray>(
      { items: [1, 2, 3] },
      { storageKey: 'array-test' }
    )
    
    const state = arrayManager.getState()
    
    // Add item
    state.items.push(4)
    expect(state.items).toEqual([1, 2, 3, 4])
    
    // Remove item
    state.items.splice(0, 1)
    expect(state.items).toEqual([2, 3, 4])
    
    // Replace array
    state.items = [10, 20]
    expect(state.items).toEqual([10, 20])
    
    arrayManager.destroy()
  })

  it('should cleanup properly on destroy', () => {
    const syncEngineSpy = vi.spyOn(manager['syncEngine'], 'destroy')
    
    manager.destroy()
    
    expect(syncEngineSpy).toHaveBeenCalled()
    syncEngineSpy.mockRestore()
  })
})
