/**
 * Generate a unique tab identifier
 * Uses crypto.randomUUID if available, falls back to timestamp + random
 */
export function generateTabId(): string {
  if (typeof crypto !== 'undefined' && crypto.randomUUID) {
    return crypto.randomUUID()
  }
  
  // Fallback for environments without crypto.randomUUID
  return `tab-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
}

/**
 * Generate a short unique identifier
 */
export function generateShortId(): string {
  return Math.random().toString(36).substr(2, 9)
}

/**
 * Generate a version number based on timestamp
 */
export function generateVersion(): number {
  return Date.now()
}
