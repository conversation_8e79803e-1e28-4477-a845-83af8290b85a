import { uniqueId } from 'lodash'

/**
 * Generate a unique tab identifier
 * Uses crypto.randomUUID if available, falls back to lodash uniqueId
 */
export function generateTabId(): string {
  if (typeof crypto !== 'undefined' && crypto.randomUUID) {
    return crypto.randomUUID()
  }

  // Fallback using lodash uniqueId with timestamp
  return `tab-${Date.now()}-${uniqueId()}`
}

/**
 * Generate a short unique identifier using lodash
 */
export function generateShortId(): string {
  return uniqueId()
}

/**
 * Generate a version number based on timestamp
 */
export function generateVersion(): number {
  return Date.now()
}
