import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { SyncEngine } from '@/core/SyncEngine'
import type { StateWrapper } from '@/types'

interface TestState {
  count: number
  text: string
}

describe('SyncEngine', () => {
  let engine1: SyncEngine<TestState>
  let engine2: SyncEngine<TestState>
  const channelName = 'test-channel'

  beforeEach(() => {
    engine1 = new SyncEngine<TestState>(channelName)
    engine2 = new SyncEngine<TestState>(channelName)
  })

  afterEach(() => {
    engine1.destroy()
    engine2.destroy()
  })

  it('should initialize with unique tab IDs', () => {
    expect(engine1.getTabId()).toBeTruthy()
    expect(engine2.getTabId()).toBeTruthy()
    expect(engine1.getTabId()).not.toBe(engine2.getTabId())
  })

  it('should start with no connected tabs', () => {
    expect(engine1.getConnectedTabsCount()).toBe(0)
    expect(engine1.isConnected()).toBe(false)
  })

  it('should discover other tabs via ping/pong', async () => {
    const connectedPromise = new Promise<void>((resolve) => {
      engine1.on('connected', () => resolve())
    })

    engine1.sendPing()
    
    await connectedPromise
    expect(engine1.getConnectedTabsCount()).toBe(1)
    expect(engine1.isConnected()).toBe(true)
  })

  it('should broadcast state updates', async () => {
    const stateWrapper: StateWrapper<TestState> = {
      data: { count: 42, text: 'hello' },
      metadata: {
        version: 1,
        lastModified: Date.now(),
        modifiedBy: engine1.getTabId()
      }
    }

    const syncPromise = new Promise<any>((resolve) => {
      engine2.on('sync', (data) => resolve(data))
    })

    engine1.broadcastUpdate(stateWrapper)
    
    const syncData = await syncPromise
    expect(syncData.remoteState).toEqual(stateWrapper)
    expect(syncData.fromTab).toBe(engine1.getTabId())
  })

  it('should resolve conflicts with last-write-wins strategy', () => {
    const localState: StateWrapper<TestState> = {
      data: { count: 1, text: 'local' },
      metadata: {
        version: 1,
        lastModified: 1000,
        modifiedBy: 'local-tab'
      }
    }

    const remoteState: StateWrapper<TestState> = {
      data: { count: 2, text: 'remote' },
      metadata: {
        version: 2,
        lastModified: 2000,
        modifiedBy: 'remote-tab'
      }
    }

    const resolved = engine1.resolveConflict(localState, remoteState)
    expect(resolved).toBe(remoteState) // Remote is newer
  })

  it('should resolve conflicts with merge-deep strategy', () => {
    const engine = new SyncEngine<any>(channelName, 'merge-deep')
    
    const localState: StateWrapper<any> = {
      data: { user: { name: 'John', age: 30 }, count: 1 },
      metadata: {
        version: 1,
        lastModified: 1000,
        modifiedBy: 'local-tab'
      }
    }

    const remoteState: StateWrapper<any> = {
      data: { user: { age: 31, city: 'NYC' }, text: 'hello' },
      metadata: {
        version: 2,
        lastModified: 2000,
        modifiedBy: 'remote-tab'
      }
    }

    const resolved = engine.resolveConflict(localState, remoteState)
    expect(resolved.data).toEqual({
      user: { name: 'John', age: 31, city: 'NYC' },
      count: 1,
      text: 'hello'
    })
    
    engine.destroy()
  })

  it('should resolve conflicts with custom function', () => {
    const customResolver = vi.fn((local, remote) => ({ ...local, ...remote, custom: true }))
    const engine = new SyncEngine<any>(channelName, customResolver)
    
    const localState: StateWrapper<any> = {
      data: { a: 1 },
      metadata: { version: 1, lastModified: 1000, modifiedBy: 'local' }
    }

    const remoteState: StateWrapper<any> = {
      data: { b: 2 },
      metadata: { version: 2, lastModified: 2000, modifiedBy: 'remote' }
    }

    const resolved = engine.resolveConflict(localState, remoteState)
    expect(customResolver).toHaveBeenCalledWith(localState.data, remoteState.data)
    expect(resolved.data).toEqual({ a: 1, b: 2, custom: true })
    
    engine.destroy()
  })

  it('should handle tab closed events', async () => {
    // First establish connection
    const connectedPromise = new Promise<void>((resolve) => {
      engine1.on('connected', () => resolve())
    })

    engine1.sendPing()
    await connectedPromise

    expect(engine1.getConnectedTabsCount()).toBe(1)

    // Now simulate tab closing
    const disconnectedPromise = new Promise<void>((resolve) => {
      engine1.on('disconnected', () => resolve())
    })

    engine2.destroy()
    
    // Wait a bit for the tab-closed message to be processed
    await new Promise(resolve => setTimeout(resolve, 10))
    
    // The ping interval should eventually clean up the connection
    // For testing, we can manually trigger the cleanup logic
  })

  it('should add and remove event listeners', () => {
    const listener = vi.fn()
    
    engine1.on('connected', listener)
    engine1.off('connected', listener)
    
    // Trigger event - listener should not be called
    engine1.sendPing()
    expect(listener).not.toHaveBeenCalled()
  })

  it('should handle debug mode', () => {
    const debugEngine = new SyncEngine<TestState>(channelName, 'last-write-wins', true)
    expect(debugEngine).toBeDefined()
    
    // Debug mode should not affect functionality
    expect(debugEngine.getTabId()).toBeTruthy()
    expect(debugEngine.isConnected()).toBe(false)
    
    debugEngine.destroy()
  })
})
