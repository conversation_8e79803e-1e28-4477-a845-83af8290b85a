# Installation
> `npm install --save @types/react-dom`

# Summary
This package contains type definitions for react-dom (https://reactjs.org).

# Details
Files were exported from https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/react-dom/v18.

### Additional Details
 * Last updated: Wed, 30 Apr 2025 10:37:29 GMT
 * Dependencies: none
 * Peer dependencies: [@types/react](https://npmjs.com/package/@types/react)

# Credits
These definitions were written by [<PERSON><PERSON>](https://asana.com), [AssureSign](http://www.assuresign.com), [Microsoft](https://microsoft.com), [<PERSON><PERSON>kas](https://github.com/<PERSON><PERSON>Z<PERSON>nskas), [<PERSON>](https://github.com/theruther4d), [<PERSON>](https://github.com/Jessid<PERSON>), and [<PERSON>](https://github.com/eps1lon).
