import { describe, it, expect, beforeEach } from 'vitest'
import { StorageAdapter } from '@/core/StorageAdapter'
import type { StateWrapper } from '@/types'

interface TestState {
  count: number
  text: string
}

describe('StorageAdapter', () => {
  let adapter: StorageAdapter<TestState>
  const storageKey = 'test-storage'

  beforeEach(() => {
    adapter = new StorageAdapter<TestState>(storageKey)
    localStorage.clear()
  })

  it('should initialize with storage key', () => {
    expect(adapter).toBeDefined()
  })

  it('should return null when no stored state exists', () => {
    const result = adapter.load()
    expect(result).toBeNull()
  })

  it('should save and load state', () => {
    const stateWrapper: StateWrapper<TestState> = {
      data: { count: 42, text: 'hello' },
      metadata: {
        version: 1,
        lastModified: Date.now(),
        modifiedBy: 'test-tab'
      }
    }

    adapter.save(stateWrapper)
    const loaded = adapter.load()

    expect(loaded).toEqual(stateWrapper)
  })

  it('should handle JSON serialization errors gracefully', () => {
    // Create a circular reference that can't be serialized
    const circularObj: any = { count: 1 }
    circularObj.self = circularObj

    const stateWrapper: StateWrapper<any> = {
      data: circularObj,
      metadata: {
        version: 1,
        lastModified: Date.now(),
        modifiedBy: 'test-tab'
      }
    }

    // Should not throw, but should handle error gracefully
    expect(() => adapter.save(stateWrapper)).not.toThrow()
  })

  it('should handle JSON parsing errors gracefully', () => {
    // Manually set invalid JSON in localStorage
    localStorage.setItem(storageKey, 'invalid-json{')
    
    const result = adapter.load()
    expect(result).toBeNull()
  })

  it('should clear stored state', () => {
    const stateWrapper: StateWrapper<TestState> = {
      data: { count: 42, text: 'hello' },
      metadata: {
        version: 1,
        lastModified: Date.now(),
        modifiedBy: 'test-tab'
      }
    }

    adapter.save(stateWrapper)
    expect(adapter.load()).not.toBeNull()

    adapter.clear()
    expect(adapter.load()).toBeNull()
  })

  it('should check storage availability', () => {
    expect(adapter.isAvailable()).toBe(true)
  })

  it('should calculate storage size', () => {
    const stateWrapper: StateWrapper<TestState> = {
      data: { count: 42, text: 'hello world' },
      metadata: {
        version: 1,
        lastModified: Date.now(),
        modifiedBy: 'test-tab'
      }
    }

    expect(adapter.getSize()).toBe(0)
    
    adapter.save(stateWrapper)
    expect(adapter.getSize()).toBeGreaterThan(0)
  })

  it('should handle debug mode', () => {
    const debugAdapter = new StorageAdapter<TestState>(storageKey, true)
    expect(debugAdapter).toBeDefined()
    
    // Debug mode should not affect functionality
    const stateWrapper: StateWrapper<TestState> = {
      data: { count: 1, text: 'test' },
      metadata: {
        version: 1,
        lastModified: Date.now(),
        modifiedBy: 'test-tab'
      }
    }

    debugAdapter.save(stateWrapper)
    const loaded = debugAdapter.load()
    expect(loaded).toEqual(stateWrapper)
  })
})
