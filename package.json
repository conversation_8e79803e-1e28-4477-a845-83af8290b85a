{"name": "valtab", "version": "0.1.0", "description": "Share React states between different tabs using Valtio as the base", "main": "dist/index.js", "module": "dist/index.esm.js", "types": "dist/index.d.ts", "files": ["dist"], "scripts": {"build": "tsup", "dev": "tsup --watch", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "type-check": "tsc --noEmit", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix"}, "keywords": ["valtio", "react", "state-management", "cross-tab", "synchronization", "browser-tabs"], "author": "Your Name", "license": "MIT", "peerDependencies": {"react": ">=16.8.0", "valtio": ">=1.0.0"}, "peerDependenciesMeta": {"react": {"optional": false}, "valtio": {"optional": false}}, "devDependencies": {"@testing-library/react": "^16.3.0", "@types/node": "^20.0.0", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "@vitest/coverage-v8": "^1.0.0", "@vitest/ui": "^1.0.0", "eslint": "^8.0.0", "jsdom": "^23.0.0", "react": "^18.0.0", "react-dom": "^18.0.0", "tsup": "^8.0.0", "typescript": "^5.0.0", "valtio": "^1.12.0", "vitest": "^1.0.0"}, "repository": {"type": "git", "url": "https://github.com/yourusername/valtab.git"}, "bugs": {"url": "https://github.com/yourusername/valtab/issues"}, "homepage": "https://github.com/yourusername/valtab#readme"}