import { useEffect, useRef, useState, useCallback } from 'react'
import { useSnapshot } from 'valtio'
import type { 
  UseSharedStateReturn, 
  SerializableState, 
  ConnectionStatus 
} from '@/types'
import type { SharedStateManager } from '@/core'

/**
 * React hook for consuming shared state across tabs
 */
export function useSharedState<T extends SerializableState>(
  manager: SharedStateManager<T>
): UseSharedStateReturn<T> {
  const [connectionStatus, setConnectionStatus] = useState<ConnectionStatus>(() => 
    manager.getConnectionStatus()
  )
  
  // Use Valtio's useSnapshot for reactive state
  const state = useSnapshot(manager.getState())
  
  // Stable references for callbacks
  const forceSync = useCallback(() => {
    manager.forceSync()
  }, [manager])
  
  const clearStorage = useCallback(() => {
    manager.clearStorage()
  }, [manager])

  // Update connection status when events occur
  useEffect(() => {
    const updateConnectionStatus = () => {
      setConnectionStatus(manager.getConnectionStatus())
    }

    // Listen to connection events
    manager.on('connected', updateConnectionStatus)
    manager.on('disconnected', updateConnectionStatus)
    manager.on('tab-added', updateConnectionStatus)
    manager.on('tab-removed', updateConnectionStatus)
    manager.on('sync', updateConnectionStatus)
    manager.on('error', updateConnectionStatus)

    // Initial status update
    updateConnectionStatus()

    return () => {
      manager.off('connected', updateConnectionStatus)
      manager.off('disconnected', updateConnectionStatus)
      manager.off('tab-added', updateConnectionStatus)
      manager.off('tab-removed', updateConnectionStatus)
      manager.off('sync', updateConnectionStatus)
      manager.off('error', updateConnectionStatus)
    }
  }, [manager])

  return {
    state,
    isConnected: connectionStatus.isConnected,
    tabId: manager.getTabId(),
    connectionStatus,
    forceSync,
    clearStorage
  }
}
