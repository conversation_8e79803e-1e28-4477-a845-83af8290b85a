!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports,require("react"),require("valtio/react")):"function"==typeof define&&define.amd?define(["exports","react","valtio/react"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).valtioReactUtils={},e.React,e.valtioReact)}(this,(function(e,t,o){"use strict";var n=Symbol();e.useProxy=function(e,i){var r=o.useSnapshot(e,i);r[n];var a=!0;return t.useLayoutEffect((function(){a=!1})),new Proxy(e,{get:function(e,t){return a?r[t]:e[t]}})}}));
