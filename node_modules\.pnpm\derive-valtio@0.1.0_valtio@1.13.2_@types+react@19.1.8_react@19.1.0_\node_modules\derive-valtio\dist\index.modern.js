import{proxy as e,subscribe as t,getVersion as n}from"valtio/vanilla";const o=new WeakMap,r=new WeakMap,s=(e,t)=>{const n=o.get(e);n&&(n[0].forEach(t=>{const{d:n}=t;e!==n&&s(n)}),++n[2],t&&n[3].add(t))},l=e=>{const t=o.get(e);t&&(--t[2],t[2]||(t[3].forEach(e=>e()),t[3].clear()),t[0].forEach(t=>{const{d:n}=t;e!==n&&l(n)}))},c=e=>{const{s:n,d:c}=e;let a=r.get(c);a||(a=[new Set],r.set(e.d,a)),a[0].add(e);let d=o.get(n);if(!d){const e=new Set,r=t(n,t=>{e.forEach(e=>{const{d:o,c:r,n:c,i:a}=e;n===o&&t.every(e=>1===e[1].length&&a.includes(e[1][0]))||e.p||(s(n,r),c?l(n):e.p=Promise.resolve().then(()=>{delete e.p,l(n)}))})},!0);d=[e,r,0,new Set],o.set(n,d)}d[0].add(e)},a=e=>{const{s:t,d:n}=e,s=r.get(n);null==s||s[0].delete(e),0===(null==s?void 0:s[0].size)&&r.delete(n);const l=o.get(t);if(l){const[n,r]=l;n.delete(e),n.size||(r(),o.delete(t))}},d=e=>{const t=r.get(e);return t?Array.from(t[0]):[]},i={add:c,remove:a,list:d};function f(t,r){const s=(null==r?void 0:r.proxy)||e({}),l=!(null==r||!r.sync),d=Object.keys(t);return d.forEach(e=>{if(Object.getOwnPropertyDescriptor(s,e))throw new Error("object property already defined");const r=t[e];let i=null;const f=()=>{if(i){if(Array.from(i).map(([e])=>((e,t)=>{const n=o.get(e);return!(null==n||!n[2]||(n[3].add(t),0))})(e,f)).some(e=>e))return;if(Array.from(i).every(([e,t])=>n(e)===t.v))return}const t=new Map,u=r(e=>(t.set(e,{v:n(e)}),e)),p=()=>{var n;t.forEach((t,n)=>{var o;const r=null==(o=i)||null==(o=o.get(n))?void 0:o.s;if(r)t.s=r;else{const o={s:n,d:s,k:e,c:f,n:l,i:d};c(o),t.s=o}}),null==(n=i)||n.forEach((e,n)=>{!t.has(n)&&e.s&&a(e.s)}),i=t};u instanceof Promise?u.finally(p):p(),s[e]=u};f()}),s}function u(e,t){const n=null!=t&&t.delete?new Set:null;d(e).forEach(e=>{const{k:o}=e;null!=t&&t.keys&&!t.keys.includes(o)||(a(e),n&&n.add(o))}),n&&n.forEach(t=>{delete e[t]})}export{f as derive,u as underive,i as unstable_deriveSubscriptions};
//# sourceMappingURL=index.modern.mjs.map
