{"version": 3, "file": "react-error-boundary.umd.js", "sources": ["../node_modules/@babel/runtime/helpers/esm/setPrototypeOf.js", "../node_modules/@babel/runtime/helpers/esm/inheritsLoose.js", "../src/index.tsx"], "sourcesContent": ["export default function _setPrototypeOf(o, p) {\n  _setPrototypeOf = Object.setPrototypeOf || function _setPrototypeOf(o, p) {\n    o.__proto__ = p;\n    return o;\n  };\n\n  return _setPrototypeOf(o, p);\n}", "import setPrototypeOf from \"./setPrototypeOf.js\";\nexport default function _inheritsLoose(subClass, superClass) {\n  subClass.prototype = Object.create(superClass.prototype);\n  subClass.prototype.constructor = subClass;\n  setPrototypeOf(subClass, superClass);\n}", "import * as React from 'react'\n\nconst changedArray = (a: Array<unknown> = [], b: Array<unknown> = []) =>\n  a.length !== b.length || a.some((item, index) => !Object.is(item, b[index]))\n\ninterface FallbackProps {\n  error: Error\n  resetErrorBoundary: (...args: Array<unknown>) => void\n}\n\ninterface ErrorBoundaryPropsWithComponent {\n  onResetKeysChange?: (\n    prevResetKeys: Array<unknown> | undefined,\n    resetKeys: Array<unknown> | undefined,\n  ) => void\n  onReset?: (...args: Array<unknown>) => void\n  onError?: (error: Error, info: {componentStack: string}) => void\n  resetKeys?: Array<unknown>\n  fallback?: never\n  FallbackComponent: React.ComponentType<FallbackProps>\n  fallbackRender?: never\n}\n\ndeclare function FallbackRender(\n  props: FallbackProps,\n): React.ReactElement<\n  unknown,\n  string | React.FunctionComponent | typeof React.Component\n> | null\n\ninterface ErrorBoundaryPropsWithRender {\n  onResetKeysChange?: (\n    prevResetKeys: Array<unknown> | undefined,\n    resetKeys: Array<unknown> | undefined,\n  ) => void\n  onReset?: (...args: Array<unknown>) => void\n  onError?: (error: Error, info: {componentStack: string}) => void\n  resetKeys?: Array<unknown>\n  fallback?: never\n  FallbackComponent?: never\n  fallbackRender: typeof FallbackRender\n}\n\ninterface ErrorBoundaryPropsWithFallback {\n  onResetKeysChange?: (\n    prevResetKeys: Array<unknown> | undefined,\n    resetKeys: Array<unknown> | undefined,\n  ) => void\n  onReset?: (...args: Array<unknown>) => void\n  onError?: (error: Error, info: {componentStack: string}) => void\n  resetKeys?: Array<unknown>\n  fallback: React.ReactElement<\n    unknown,\n    string | React.FunctionComponent | typeof React.Component\n  > | null\n  FallbackComponent?: never\n  fallbackRender?: never\n}\n\ntype ErrorBoundaryProps =\n  | ErrorBoundaryPropsWithFallback\n  | ErrorBoundaryPropsWithComponent\n  | ErrorBoundaryPropsWithRender\n\ntype ErrorBoundaryState = {error: Error | null}\n\nconst initialState: ErrorBoundaryState = {error: null}\n\nclass ErrorBoundary extends React.Component<\n  React.PropsWithRef<React.PropsWithChildren<ErrorBoundaryProps>>,\n  ErrorBoundaryState\n> {\n  static getDerivedStateFromError(error: Error) {\n    return {error}\n  }\n\n  state = initialState\n  resetErrorBoundary = (...args: Array<unknown>) => {\n    this.props.onReset?.(...args)\n    this.reset()\n  }\n\n  reset() {\n    this.setState(initialState)\n  }\n\n  componentDidCatch(error: Error, info: React.ErrorInfo) {\n    this.props.onError?.(error, info)\n  }\n\n  componentDidUpdate(\n    prevProps: ErrorBoundaryProps,\n    prevState: ErrorBoundaryState,\n  ) {\n    const {error} = this.state\n    const {resetKeys} = this.props\n\n    // There's an edge case where if the thing that triggered the error\n    // happens to *also* be in the resetKeys array, we'd end up resetting\n    // the error boundary immediately. This would likely trigger a second\n    // error to be thrown.\n    // So we make sure that we don't check the resetKeys on the first call\n    // of cDU after the error is set\n\n    if (\n      error !== null &&\n      prevState.error !== null &&\n      changedArray(prevProps.resetKeys, resetKeys)\n    ) {\n      this.props.onResetKeysChange?.(prevProps.resetKeys, resetKeys)\n      this.reset()\n    }\n  }\n\n  render() {\n    const {error} = this.state\n\n    const {fallbackRender, FallbackComponent, fallback} = this.props\n\n    if (error !== null) {\n      const props = {\n        error,\n        resetErrorBoundary: this.resetErrorBoundary,\n      }\n      if (React.isValidElement(fallback)) {\n        return fallback\n      } else if (typeof fallbackRender === 'function') {\n        return fallbackRender(props)\n      } else if (FallbackComponent) {\n        return <FallbackComponent {...props} />\n      } else {\n        throw new Error(\n          'react-error-boundary requires either a fallback, fallbackRender, or FallbackComponent prop',\n        )\n      }\n    }\n\n    return this.props.children\n  }\n}\n\nfunction withErrorBoundary<P>(\n  Component: React.ComponentType<P>,\n  errorBoundaryProps: ErrorBoundaryProps,\n): React.ComponentType<P> {\n  const Wrapped: React.ComponentType<P> = props => {\n    return (\n      <ErrorBoundary {...errorBoundaryProps}>\n        <Component {...props} />\n      </ErrorBoundary>\n    )\n  }\n\n  // Format for display in DevTools\n  const name = Component.displayName || Component.name || 'Unknown'\n  Wrapped.displayName = `withErrorBoundary(${name})`\n\n  return Wrapped\n}\n\nfunction useErrorHandler(givenError?: unknown): (error: unknown) => void {\n  const [error, setError] = React.useState<unknown>(null)\n  if (givenError != null) throw givenError\n  if (error != null) throw error\n  return setError\n}\n\nexport {ErrorBoundary, withErrorBoundary, useErrorHandler}\nexport type {\n  FallbackProps,\n  ErrorBoundaryPropsWithComponent,\n  ErrorBoundaryPropsWithRender,\n  ErrorBoundaryPropsWithFallback,\n  ErrorBoundaryProps,\n}\n\n/*\neslint\n  @typescript-eslint/sort-type-union-intersection-members: \"off\",\n  @typescript-eslint/no-throw-literal: \"off\",\n  @typescript-eslint/prefer-nullish-coalescing: \"off\"\n*/\n"], "names": ["_setPrototypeOf", "o", "p", "Object", "setPrototypeOf", "__proto__", "_inherits<PERSON><PERSON>e", "subClass", "superClass", "prototype", "create", "constructor", "changedArray", "a", "b", "length", "some", "item", "index", "is", "initialState", "error", "Error<PERSON>ou<PERSON><PERSON>", "state", "resetErrorBoundary", "args", "props", "onReset", "reset", "getDerivedStateFromError", "setState", "componentDidCatch", "info", "onError", "componentDidUpdate", "prevProps", "prevState", "resetKeys", "onResetKeysChange", "render", "fallback<PERSON><PERSON>", "FallbackComponent", "fallback", "React", "isValidElement", "Error", "children", "Component", "with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "errorBoundaryProps", "Wrapped", "name", "displayName", "useErrorHandler", "given<PERSON><PERSON><PERSON>", "useState", "setError"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;EAAe,SAASA,eAAT,CAAyBC,CAAzB,EAA4BC,CAA5B,EAA+B;EAC5CF,EAAAA,eAAe,GAAGG,MAAM,CAACC,cAAP,IAAyB,SAASJ,eAAT,CAAyBC,CAAzB,EAA4BC,CAA5B,EAA+B;EACxED,IAAAA,CAAC,CAACI,SAAF,GAAcH,CAAd;EACA,WAAOD,CAAP;EACD,GAHD;;EAKA,SAAOD,eAAe,CAACC,CAAD,EAAIC,CAAJ,CAAtB;EACD;;ECNc,SAASI,cAAT,CAAwBC,QAAxB,EAAkCC,UAAlC,EAA8C;EAC3DD,EAAAA,QAAQ,CAACE,SAAT,GAAqBN,MAAM,CAACO,MAAP,CAAcF,UAAU,CAACC,SAAzB,CAArB;EACAF,EAAAA,QAAQ,CAACE,SAAT,CAAmBE,WAAnB,GAAiCJ,QAAjC;EACAH,EAAAA,eAAc,CAACG,QAAD,EAAWC,UAAX,CAAd;EACD;;ECHD,IAAMI,YAAY,GAAG,SAAfA,YAAe,CAACC,CAAD,EAAyBC,CAAzB;EAAA,MAACD,CAAD;EAACA,IAAAA,CAAD,GAAqB,EAArB;EAAA;;EAAA,MAAyBC,CAAzB;EAAyBA,IAAAA,CAAzB,GAA6C,EAA7C;EAAA;;EAAA,SACnBD,CAAC,CAACE,MAAF,KAAaD,CAAC,CAACC,MAAf,IAAyBF,CAAC,CAACG,IAAF,CAAO,UAACC,IAAD,EAAOC,KAAP;EAAA,WAAiB,CAACf,MAAM,CAACgB,EAAP,CAAUF,IAAV,EAAgBH,CAAC,CAACI,KAAD,CAAjB,CAAlB;EAAA,GAAP,CADN;EAAA,CAArB;;EAgEA,IAAME,YAAgC,GAAG;EAACC,EAAAA,KAAK,EAAE;EAAR,CAAzC;;MAEMC;;;;;;;;;;;YAQJC,QAAQH;;YACRI,qBAAqB,YAA6B;EAAA;;EAAA,yCAAzBC,IAAyB;EAAzBA,QAAAA,IAAyB;EAAA;;EAChD,YAAKC,KAAL,CAAWC,OAAX,yCAAKD,KAAL,EAAWC,OAAX,oBAAwBF,IAAxB;;EACA,YAAKG,KAAL;EACD;;;;;kBARMC,2BAAP,kCAAgCR,KAAhC,EAA8C;EAC5C,WAAO;EAACA,MAAAA,KAAK,EAALA;EAAD,KAAP;EACD;;;;WAQDO,QAAA,iBAAQ;EACN,SAAKE,QAAL,CAAcV,YAAd;EACD;;WAEDW,oBAAA,2BAAkBV,KAAlB,EAAgCW,IAAhC,EAAuD;EAAA;;EACrD,gDAAKN,KAAL,EAAWO,OAAX,4DAAqBZ,KAArB,EAA4BW,IAA5B;EACD;;WAEDE,qBAAA,4BACEC,SADF,EAEEC,SAFF,EAGE;EACA,QAAOf,KAAP,GAAgB,KAAKE,KAArB,CAAOF,KAAP;EACA,QAAOgB,SAAP,GAAoB,KAAKX,KAAzB,CAAOW,SAAP,CAFA;EAKA;EACA;EACA;EACA;EACA;;EAEA,QACEhB,KAAK,KAAK,IAAV,IACAe,SAAS,CAACf,KAAV,KAAoB,IADpB,IAEAT,YAAY,CAACuB,SAAS,CAACE,SAAX,EAAsBA,SAAtB,CAHd,EAIE;EAAA;;EACA,oDAAKX,KAAL,EAAWY,iBAAX,8DAA+BH,SAAS,CAACE,SAAzC,EAAoDA,SAApD;EACA,WAAKT,KAAL;EACD;EACF;;WAEDW,SAAA,kBAAS;EACP,QAAOlB,KAAP,GAAgB,KAAKE,KAArB,CAAOF,KAAP;EAEA,uBAAsD,KAAKK,KAA3D;EAAA,QAAOc,cAAP,gBAAOA,cAAP;EAAA,QAAuBC,iBAAvB,gBAAuBA,iBAAvB;EAAA,QAA0CC,QAA1C,gBAA0CA,QAA1C;;EAEA,QAAIrB,KAAK,KAAK,IAAd,EAAoB;EAClB,UAAMK,MAAK,GAAG;EACZL,QAAAA,KAAK,EAALA,KADY;EAEZG,QAAAA,kBAAkB,EAAE,KAAKA;EAFb,OAAd;;EAIA,wBAAImB,gBAAK,CAACC,cAAN,CAAqBF,QAArB,CAAJ,EAAoC;EAClC,eAAOA,QAAP;EACD,OAFD,MAEO,IAAI,OAAOF,cAAP,KAA0B,UAA9B,EAA0C;EAC/C,eAAOA,cAAc,CAACd,MAAD,CAArB;EACD,OAFM,MAEA,IAAIe,iBAAJ,EAAuB;EAC5B,4BAAOE,+BAAC,iBAAD,EAAuBjB,MAAvB,CAAP;EACD,OAFM,MAEA;EACL,cAAM,IAAImB,KAAJ,CACJ,4FADI,CAAN;EAGD;EACF;;EAED,WAAO,KAAKnB,KAAL,CAAWoB,QAAlB;EACD;;;IAtEyBH,gBAAK,CAACI;;EAyElC,SAASC,iBAAT,CACED,SADF,EAEEE,kBAFF,EAG0B;EACxB,MAAMC,OAA+B,GAAG,SAAlCA,OAAkC,CAAAxB,KAAK,EAAI;EAC/C,wBACEiB,+BAAC,aAAD,EAAmBM,kBAAnB,eACEN,+BAAC,SAAD,EAAejB,KAAf,CADF,CADF;EAKD,GAND,CADwB;;;EAUxB,MAAMyB,IAAI,GAAGJ,SAAS,CAACK,WAAV,IAAyBL,SAAS,CAACI,IAAnC,IAA2C,SAAxD;EACAD,EAAAA,OAAO,CAACE,WAAR,0BAA2CD,IAA3C;EAEA,SAAOD,OAAP;EACD;;EAED,SAASG,eAAT,CAAyBC,UAAzB,EAAyE;EACvE,wBAA0BX,gBAAK,CAACY,QAAN,CAAwB,IAAxB,CAA1B;EAAA,MAAOlC,KAAP;EAAA,MAAcmC,QAAd;;EACA,MAAIF,UAAU,IAAI,IAAlB,EAAwB,MAAMA,UAAN;EACxB,MAAIjC,KAAK,IAAI,IAAb,EAAmB,MAAMA,KAAN;EACnB,SAAOmC,QAAP;EACD;EAWD;EACA;EACA;EACA;EACA;EACA;;;;;;;;;;;;"}