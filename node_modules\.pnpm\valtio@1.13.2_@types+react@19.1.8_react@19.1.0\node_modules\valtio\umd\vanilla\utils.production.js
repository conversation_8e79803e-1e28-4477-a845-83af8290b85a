!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports,require("valtio/vanilla"),require("derive-valtio")):"function"==typeof define&&define.amd?define(["exports","valtio/vanilla","derive-valtio"],e):e((t="undefined"!=typeof globalThis?globalThis:t||self).valtioVanillaUtils={},t.valtio<PERSON>anilla,t.deriveValtio)}(this,(function(t,e,r){"use strict";function n(t,e,r,n){var o={configurable:!0,enumerable:!0};return o[t]=n,Object.defineProperty(e,r,o)}function o(){o=function(){return e};var t,e={},r=Object.prototype,n=r.hasOwnProperty,i=Object.defineProperty||function(t,e,r){t[e]=r.value},a="function"==typeof Symbol?Symbol:{},u=a.iterator||"@@iterator",c=a.asyncIterator||"@@asyncIterator",s=a.toStringTag||"@@toStringTag";function f(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{f({},"")}catch(t){f=function(t,e,r){return t[e]=r}}function l(t,e,r,n){var o=e&&e.prototype instanceof g?e:g,a=Object.create(o.prototype),u=new k(n||[]);return i(a,"_invoke",{value:_(t,r,u)}),a}function h(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}e.wrap=l;var p="suspendedStart",d="suspendedYield",y="executing",v="completed",b={};function g(){}function m(){}function w(){}var O={};f(O,u,(function(){return this}));var x=Object.getPrototypeOf,j=x&&x(x(A([])));j&&j!==r&&n.call(j,u)&&(O=j);var E=w.prototype=g.prototype=Object.create(O);function S(t){["next","throw","return"].forEach((function(e){f(t,e,(function(t){return this._invoke(e,t)}))}))}function L(t,e){function r(o,i,a,u){var c=h(t[o],t,i);if("throw"!==c.type){var s=c.arg,f=s.value;return f&&"object"==typeof f&&n.call(f,"__await")?e.resolve(f.__await).then((function(t){r("next",t,a,u)}),(function(t){r("throw",t,a,u)})):e.resolve(f).then((function(t){s.value=t,a(s)}),(function(t){return r("throw",t,a,u)}))}u(c.arg)}var o;i(this,"_invoke",{value:function(t,n){function i(){return new e((function(e,o){r(t,n,e,o)}))}return o=o?o.then(i,i):i()}})}function _(e,r,n){var o=p;return function(i,a){if(o===y)throw new Error("Generator is already running");if(o===v){if("throw"===i)throw a;return{value:t,done:!0}}for(n.method=i,n.arg=a;;){var u=n.delegate;if(u){var c=P(u,n);if(c){if(c===b)continue;return c}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(o===p)throw o=v,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);o=y;var s=h(e,r,n);if("normal"===s.type){if(o=n.done?v:d,s.arg===b)continue;return{value:s.arg,done:n.done}}"throw"===s.type&&(o=v,n.method="throw",n.arg=s.arg)}}}function P(e,r){var n=r.method,o=e.iterator[n];if(o===t)return r.delegate=null,"throw"===n&&e.iterator.return&&(r.method="return",r.arg=t,P(e,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),b;var i=h(o,e.iterator,r.arg);if("throw"===i.type)return r.method="throw",r.arg=i.arg,r.delegate=null,b;var a=i.arg;return a?a.done?(r[e.resultName]=a.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,b):a:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,b)}function T(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function N(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function k(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(T,this),this.reset(!0)}function A(e){if(e||""===e){var r=e[u];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var o=-1,i=function r(){for(;++o<e.length;)if(n.call(e,o))return r.value=e[o],r.done=!1,r;return r.value=t,r.done=!0,r};return i.next=i}}throw new TypeError(typeof e+" is not iterable")}return m.prototype=w,i(E,"constructor",{value:w,configurable:!0}),i(w,"constructor",{value:m,configurable:!0}),m.displayName=f(w,s,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===m||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,w):(t.__proto__=w,f(t,s,"GeneratorFunction")),t.prototype=Object.create(E),t},e.awrap=function(t){return{__await:t}},S(L.prototype),f(L.prototype,c,(function(){return this})),e.AsyncIterator=L,e.async=function(t,r,n,o,i){void 0===i&&(i=Promise);var a=new L(l(t,r,n,o),i);return e.isGeneratorFunction(r)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},S(E),f(E,s,"Generator"),f(E,u,(function(){return this})),f(E,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},e.values=A,k.prototype={constructor:k,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(N),!e)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var r=this;function o(n,o){return u.type="throw",u.arg=e,r.next=n,o&&(r.method="next",r.arg=t),!!o}for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],u=a.completion;if("root"===a.tryLoc)return o("end");if(a.tryLoc<=this.prev){var c=n.call(a,"catchLoc"),s=n.call(a,"finallyLoc");if(c&&s){if(this.prev<a.catchLoc)return o(a.catchLoc,!0);if(this.prev<a.finallyLoc)return o(a.finallyLoc)}else if(c){if(this.prev<a.catchLoc)return o(a.catchLoc,!0)}else{if(!s)throw new Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return o(a.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var o=this.tryEntries[r];if(o.tryLoc<=this.prev&&n.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=t,a.arg=e,i?(this.method="next",this.next=i.finallyLoc,b):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),b},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),N(r),b}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var o=n.arg;N(r)}return o}}throw new Error("illegal catch attempt")},delegateYield:function(e,r,n){return this.delegate={iterator:A(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=t),b}},e}function i(t,e,r,n,o,i,a){try{var u=t[i](a),c=u.value}catch(t){return void r(t)}u.done?e(c):Promise.resolve(c).then(n,o)}function a(){return a=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},a.apply(this,arguments)}var u;var c=["enabled","name"],s=Symbol();var f,l=function t(r){if(f||(f=e.unstable_buildProxyFunction()[2]),"object"!=typeof(n=r)||null===n||f.has(r))return r;var n,o=Array.isArray(r)?[]:Object.create(Object.getPrototypeOf(r));return Reflect.ownKeys(r).forEach((function(e){o[e]=t(r[e])})),o};Object.defineProperty(t,"derive",{enumerable:!0,get:function(){return r.derive}}),Object.defineProperty(t,"underive",{enumerable:!0,get:function(){return r.underive}}),Object.defineProperty(t,"unstable_deriveSubscriptions",{enumerable:!0,get:function(){return r.unstable_deriveSubscriptions}}),t.addComputed=function(t,e,n){void 0===n&&(n=t);var o={};return Object.keys(e).forEach((function(r){o[r]=function(n){return e[r](n(t))}})),r.derive(o,{proxy:n})},t.devtools=function(t,r){"string"==typeof r&&(console.warn("string name option is deprecated, use { name }. https://github.com/pmndrs/valtio/pull/400"),r={name:r});var n,o=r||{},i=o.enabled,u=o.name,f=void 0===u?"":u,l=function(t,e){if(null==t)return{};var r,n,o={},i=Object.keys(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||(o[r]=t[r]);return o}(o,c);try{n=null!=i&&i&&window.__REDUX_DEVTOOLS_EXTENSION__}catch(t){}if(n){var h=!1,p=n.connect(a({name:f},l)),d=e.subscribe(t,(function(r){var n=r.filter((function(t){return t[0],t[1][0]!==s})).map((function(t){return t[0]+":"+t[1].map(String).join(".")})).join(", ");if(n)if(h)h=!1;else{var o=Object.assign({},e.snapshot(t));delete o[s],p.send({type:n,updatedAt:(new Date).toLocaleString()},o)}})),y=p.subscribe((function(r){var n,o;if("ACTION"===r.type&&r.payload)try{Object.assign(t,JSON.parse(r.payload))}catch(t){console.error("please dispatch a serializable value that JSON.parse() and proxy() support\n",t)}if("DISPATCH"===r.type&&r.state){var i,a;if("JUMP_TO_ACTION"===(null==(i=r.payload)?void 0:i.type)||"JUMP_TO_STATE"===(null==(a=r.payload)?void 0:a.type)){h=!0;var u=JSON.parse(r.state);Object.assign(t,u)}t[s]=r}else if("DISPATCH"===r.type&&"COMMIT"===(null==(n=r.payload)?void 0:n.type))p.init(e.snapshot(t));else if("DISPATCH"===r.type&&"IMPORT_STATE"===(null==(o=r.payload)?void 0:o.type)){var c,f,l=null==(c=r.payload.nextLiftedState)?void 0:c.actionsById,d=(null==(f=r.payload.nextLiftedState)?void 0:f.computedStates)||[];h=!0,d.forEach((function(r,n){var o=r.state,i=l[n]||"No action found";Object.assign(t,o),0===n?p.init(e.snapshot(t)):p.send(i,e.snapshot(t))}))}}));return p.init(e.snapshot(t)),function(){d(),null==y||y()}}},t.proxyMap=function(t){var r,o=e.proxy((n("get",r={data:Array.from(t||[]),has:function(t){return this.data.some((function(e){return e[0]===t}))},set:function(t,e){var r=this.data.find((function(e){return e[0]===t}));return r?r[1]=e:this.data.push([t,e]),this},get:function(t){var e;return null==(e=this.data.find((function(e){return e[0]===t})))?void 0:e[1]},delete:function(t){var e=this.data.findIndex((function(e){return e[0]===t}));return-1!==e&&(this.data.splice(e,1),!0)},clear:function(){this.data.splice(0)},get size(){return this.data.length},toJSON:function(){return new Map(this.data)},forEach:function(t){var e=this;this.data.forEach((function(r){t(r[1],r[0],e)}))},keys:function(){return this.data.map((function(t){return t[0]})).values()},values:function(){return this.data.map((function(t){return t[1]})).values()},entries:function(){return new Map(this.data).entries()}},Symbol.toStringTag,(function(){return"Map"})),r[Symbol.iterator]=function(){return this.entries()},r));return Object.defineProperties(o,{data:{enumerable:!1},size:{enumerable:!1},toJSON:{enumerable:!1}}),Object.seal(o),o},t.proxySet=function(t){var r,o=e.proxy((n("get",r={data:Array.from(new Set(t)),has:function(t){return-1!==this.data.indexOf(t)},add:function(t){var r=!1;return"object"==typeof t&&null!==t&&(r=-1!==this.data.indexOf(e.proxy(t))),-1!==this.data.indexOf(t)||r||this.data.push(t),this},delete:function(t){var e=this.data.indexOf(t);return-1!==e&&(this.data.splice(e,1),!0)},clear:function(){this.data.splice(0)},get size(){return this.data.length},forEach:function(t){var e=this;this.data.forEach((function(r){t(r,r,e)}))}},Symbol.toStringTag,(function(){return"Set"})),r.toJSON=function(){return new Set(this.data)},r[Symbol.iterator]=function(){return this.data[Symbol.iterator]()},r.values=function(){return this.data.values()},r.keys=function(){return this.data.values()},r.entries=function(){return new Set(this.data).entries()},r));return Object.defineProperties(o,{data:{enumerable:!1},size:{enumerable:!1},toJSON:{enumerable:!1}}),Object.seal(o),o},t.proxyWithComputed=function(t,r){Object.keys(r).forEach((function(o){if(Object.getOwnPropertyDescriptor(t,o))throw new Error("object property already defined");var i=r[o],a="function"==typeof i?{get:i}:i,u=a.get,c=a.set,s={get:function(){return u(e.snapshot(n))}};c&&(s.set=function(t){return c(n,t)}),Object.defineProperty(t,o,s)}));var n=e.proxy(t);return n},t.proxyWithHistory=function(t,r){void 0===r&&(r=!1);var n=e.proxy({value:t,history:e.ref({wip:void 0,snapshots:[],index:-1}),clone:l,canUndo:function(){return n.history.index>0},undo:function(){n.canUndo()&&(n.value=n.history.wip=n.clone(n.history.snapshots[--n.history.index]))},canRedo:function(){return n.history.index<n.history.snapshots.length-1},redo:function(){n.canRedo()&&(n.value=n.history.wip=n.clone(n.history.snapshots[++n.history.index]))},saveHistory:function(){n.history.snapshots.splice(n.history.index+1),n.history.snapshots.push(e.snapshot(n).value),++n.history.index},subscribe:function(){return e.subscribe(n,(function(t){t.every((function(t){return"value"===t[1][0]&&("set"!==t[0]||t[2]!==n.history.wip)}))&&n.saveHistory()}))}});return n.saveHistory(),r||n.subscribe(),n},t.subscribeKey=function(t,r,n,o){var i=t[r];return e.subscribe(t,(function(){var e=t[r];Object.is(i,e)||n(i=e)}),o)},t.watch=function(t,r){var n,a,c=!0,s=new Set,f=new Map,l=function(){c&&(c=!1,s.forEach((function(t){return t()})),s.clear(),f.forEach((function(t){return t()})),f.clear())},h=(n=o().mark((function n(){var i,a,p,d;return o().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:if(c){n.next=2;break}return n.abrupt("return");case 2:if(s.forEach((function(t){return t()})),s.clear(),i=new Set,a=u,u=s,n.prev=7,!((p=t((function(t){if(i.add(t),c&&!f.has(t)){var n=e.subscribe(t,h,null==r?void 0:r.sync);f.set(t,n)}return t})))&&p instanceof Promise)){n.next=15;break}return n.next=12,p;case 12:n.t0=n.sent,n.next=16;break;case 15:n.t0=p;case 16:(d=n.t0)&&(c?s.add(d):l());case 18:return n.prev=18,u=a,n.finish(18);case 21:f.forEach((function(t,e){i.has(e)||(f.delete(e),t())}));case 22:case"end":return n.stop()}}),n,null,[[7,,18,21]])})),a=function(){var t=this,e=arguments;return new Promise((function(r,o){var a=n.apply(t,e);function u(t){i(a,r,o,u,c,"next",t)}function c(t){i(a,r,o,u,c,"throw",t)}u(void 0)}))},function(){return a.apply(this,arguments)});return u&&u.add(l),h(),l}}));
