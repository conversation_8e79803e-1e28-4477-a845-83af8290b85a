!function(e,n){"object"==typeof exports&&"undefined"!=typeof module?module.exports=n(require("@babel/helper-module-imports"),require("@babel/types"),require("babel-plugin-macros")):"function"==typeof define&&define.amd?define(["@babel/helper-module-imports","@babel/types","babel-plugin-macros"],n):(e="undefined"!=typeof globalThis?globalThis:e||self).valtioMacro=n(e.helperModuleImports,e.t,e.babelPluginMacros)}(this,(function(e,n,t){"use strict";function r(e){var n=Object.create(null);return e&&Object.keys(e).forEach((function(t){if("default"!==t){var r=Object.getOwnPropertyDescriptor(e,t);Object.defineProperty(n,t,r.get?r:{enumerable:!0,get:function(){return e[t]}})}})),n.default=e,Object.freeze(n)}var a=r(n);return t.createMacro((function(n){var r;null==(r=n.references.useProxy)||r.forEach((function(n){var r,o,i,l=e.addNamed(n,"useSnapshot","valtio"),u=null==(r=n.parentPath)||null==(r=r.get("arguments.0"))?void 0:r.node;if(!a.isIdentifier(u))throw new t.MacroError("no proxy object");var c=a.identifier("valtio_macro_snap_"+u.name);null==(o=n.parentPath)||null==(o=o.parentPath)||o.replaceWith(a.variableDeclaration("const",[a.variableDeclarator(c,a.callExpression(l,[u]))]));var f=0;null==(i=n.parentPath)||null==(i=i.getFunctionParent())||i.traverse({Identifier:function(e){0===f&&e.node!==u&&e.node.name===u.name&&(e.node.name=c.name)},Function:{enter:function(){++f},exit:function(){--f}}})}))}),{configName:"valtio"})}));
