/**
 * Configuration options for creating a shared state
 */
export interface SharedStateOptions {
  /**
   * Unique key for localStorage persistence
   * @default 'valtab-state'
   */
  storageKey?: string

  /**
   * Whether to persist state to localStorage
   * @default true
   */
  persist?: boolean

  /**
   * Whether to save state when tab/window closes
   * @default true
   */
  persistOnClose?: boolean

  /**
   * Custom channel name for BroadcastChannel
   * If not provided, will use storageKey
   */
  channelName?: string

  /**
   * Custom conflict resolution strategy
   * @default 'last-write-wins'
   */
  conflictResolution?: ConflictResolutionStrategy

  /**
   * Whether to enable debug logging
   * @default false
   */
  debug?: boolean
}

/**
 * Conflict resolution strategies
 */
export type ConflictResolutionStrategy = 
  | 'last-write-wins'
  | 'merge-deep'
  | 'merge-shallow'
  | ((local: any, remote: any, path?: string[]) => any)

/**
 * Message types for cross-tab communication
 */
export interface SyncMessage {
  type: 'state-update' | 'ping' | 'pong' | 'tab-closed'
  tabId: string
  timestamp: number
  data?: any
  path?: string[]
}

/**
 * Connection status for a shared state
 */
export interface ConnectionStatus {
  isConnected: boolean
  connectedTabs: number
  lastSync: number | null
  errors: string[]
}

/**
 * Return type for useSharedState hook
 */
export interface UseSharedStateReturn<T extends object> {
  /**
   * The reactive state snapshot (read-only)
   */
  state: T

  /**
   * Whether the tab is connected to other tabs
   */
  isConnected: boolean

  /**
   * Unique identifier for this tab
   */
  tabId: string

  /**
   * Connection status details
   */
  connectionStatus: ConnectionStatus

  /**
   * Force sync with other tabs
   */
  forceSync: () => void

  /**
   * Clear all persisted data
   */
  clearStorage: () => void
}

/**
 * Internal state wrapper with metadata
 */
export interface StateWrapper<T extends object> {
  data: T
  metadata: {
    version: number
    lastModified: number
    modifiedBy: string
  }
}

/**
 * Event types for state manager
 */
export type StateManagerEvent = 
  | 'connected'
  | 'disconnected'
  | 'sync'
  | 'error'
  | 'tab-added'
  | 'tab-removed'

/**
 * Event listener callback
 */
export type EventListener<T = any> = (data: T) => void

/**
 * Serializable state constraint
 */
export type SerializableState = Record<string, any>
