{"version": "1.6.1", "results": [[":src/test/useSharedState.test.tsx", {"duration": 0, "failed": true}], [":src/test/StorageAdapter.test.ts", {"duration": 17, "failed": false}], [":src/test/SyncEngine.test.ts", {"duration": 63, "failed": false}], [":src/test/utils.test.ts", {"duration": 8, "failed": false}], [":src/test/SharedStateManager.test.ts", {"duration": 18, "failed": false}], [":src/test/integration.test.ts", {"duration": 168, "failed": true}]]}