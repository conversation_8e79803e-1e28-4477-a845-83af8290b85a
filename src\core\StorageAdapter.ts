import type { StateWrapper, SerializableState } from '@/types'
import { deepClone } from '@/utils'

/**
 * Handles localStorage persistence for shared state
 */
export class StorageAdapter<T extends SerializableState> {
  private storageKey: string
  private debug: boolean

  constructor(storageKey: string, debug = false) {
    this.storageKey = storageKey
    this.debug = debug
  }

  /**
   * Load state from localStorage
   */
  load(): StateWrapper<T> | null {
    try {
      const stored = localStorage.getItem(this.storageKey)
      if (!stored) {
        this.log('No stored state found')
        return null
      }

      const parsed = JSON.parse(stored) as StateWrapper<T>
      this.log('Loaded state from storage:', parsed)
      return parsed
    } catch (error) {
      console.error('Failed to load state from storage:', error)
      return null
    }
  }

  /**
   * Save state to localStorage
   */
  save(stateWrapper: StateWrapper<T>): void {
    try {
      const serialized = JSON.stringify(stateWrapper)
      localStorage.setItem(this.storageKey, serialized)
      this.log('Saved state to storage:', stateWrapper)
    } catch (error) {
      console.error('Failed to save state to storage:', error)
    }
  }

  /**
   * Clear stored state
   */
  clear(): void {
    try {
      localStorage.removeItem(this.storageKey)
      this.log('Cleared stored state')
    } catch (error) {
      console.error('Failed to clear stored state:', error)
    }
  }

  /**
   * Check if storage is available
   */
  isAvailable(): boolean {
    try {
      const testKey = '__valtab_test__'
      localStorage.setItem(testKey, 'test')
      localStorage.removeItem(testKey)
      return true
    } catch {
      return false
    }
  }

  /**
   * Get storage size in bytes (approximate)
   */
  getSize(): number {
    try {
      const stored = localStorage.getItem(this.storageKey)
      return stored ? new Blob([stored]).size : 0
    } catch {
      return 0
    }
  }

  private log(...args: any[]): void {
    if (this.debug) {
      console.log('[StorageAdapter]', ...args)
    }
  }
}
