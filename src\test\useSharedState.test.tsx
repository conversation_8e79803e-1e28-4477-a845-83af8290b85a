import { describe, it, expect, beforeEach, afterEach } from 'vitest'
import { renderHook, act } from '@testing-library/react'
import { useSharedState } from '@/react/useSharedState'
import { SharedStateManager } from '@/core/SharedStateManager'

interface TestState {
  count: number
  text: string
}

describe('useSharedState', () => {
  let manager: SharedStateManager<TestState>
  const initialState: TestState = { count: 0, text: 'hello' }

  beforeEach(() => {
    localStorage.clear()
    manager = new SharedStateManager(initialState, {
      storageKey: 'test-hook-state',
      debug: false
    })
  })

  afterEach(() => {
    manager.destroy()
  })

  it('should return initial state', () => {
    const { result } = renderHook(() => useSharedState(manager))
    
    expect(result.current.state.count).toBe(0)
    expect(result.current.state.text).toBe('hello')
    expect(result.current.isConnected).toBe(false)
    expect(result.current.tabId).toBeTruthy()
  })

  it('should provide connection status', () => {
    const { result } = renderHook(() => useSharedState(manager))
    
    const { connectionStatus } = result.current
    expect(connectionStatus.isConnected).toBe(false)
    expect(connectionStatus.connectedTabs).toBe(0)
    expect(connectionStatus.lastSync).toBeNull()
    expect(connectionStatus.errors).toEqual([])
  })

  it('should provide stable callback references', () => {
    const { result, rerender } = renderHook(() => useSharedState(manager))
    
    const { forceSync: forceSync1, clearStorage: clearStorage1 } = result.current
    
    rerender()
    
    const { forceSync: forceSync2, clearStorage: clearStorage2 } = result.current
    
    expect(forceSync1).toBe(forceSync2)
    expect(clearStorage1).toBe(clearStorage2)
  })

  it('should react to state changes', () => {
    const { result } = renderHook(() => useSharedState(manager))
    
    expect(result.current.state.count).toBe(0)
    
    act(() => {
      const state = manager.getState()
      state.count = 42
    })
    
    expect(result.current.state.count).toBe(42)
  })

  it('should react to nested state changes', () => {
    interface NestedState {
      user: {
        name: string
        age: number
      }
      settings: {
        theme: string
      }
    }
    
    const nestedManager = new SharedStateManager<NestedState>({
      user: { name: 'John', age: 30 },
      settings: { theme: 'dark' }
    }, { storageKey: 'nested-test' })
    
    const { result } = renderHook(() => useSharedState(nestedManager))
    
    expect(result.current.state.user.name).toBe('John')
    expect(result.current.state.settings.theme).toBe('dark')
    
    act(() => {
      const state = nestedManager.getState()
      state.user.name = 'Jane'
      state.settings.theme = 'light'
    })
    
    expect(result.current.state.user.name).toBe('Jane')
    expect(result.current.state.settings.theme).toBe('light')
    
    nestedManager.destroy()
  })

  it('should handle array state changes', () => {
    interface ArrayState {
      items: string[]
      count: number
    }
    
    const arrayManager = new SharedStateManager<ArrayState>({
      items: ['a', 'b'],
      count: 2
    }, { storageKey: 'array-test' })
    
    const { result } = renderHook(() => useSharedState(arrayManager))
    
    expect(result.current.state.items).toEqual(['a', 'b'])
    expect(result.current.state.count).toBe(2)
    
    act(() => {
      const state = arrayManager.getState()
      state.items.push('c')
      state.count = state.items.length
    })
    
    expect(result.current.state.items).toEqual(['a', 'b', 'c'])
    expect(result.current.state.count).toBe(3)
    
    arrayManager.destroy()
  })

  it('should call forceSync', () => {
    const { result } = renderHook(() => useSharedState(manager))
    
    expect(() => {
      result.current.forceSync()
    }).not.toThrow()
  })

  it('should call clearStorage', () => {
    const { result } = renderHook(() => useSharedState(manager))
    
    expect(() => {
      result.current.clearStorage()
    }).not.toThrow()
  })

  it('should update connection status when events occur', async () => {
    const { result } = renderHook(() => useSharedState(manager))
    
    expect(result.current.connectionStatus.isConnected).toBe(false)
    
    // Simulate connection event
    act(() => {
      manager['syncEngine']['emit']('connected', { connectedTabs: 1 })
    })
    
    // Connection status should update
    expect(result.current.connectionStatus.isConnected).toBe(true)
  })

  it('should cleanup event listeners on unmount', () => {
    const { unmount } = renderHook(() => useSharedState(manager))
    
    // Should not throw when unmounting
    expect(() => {
      unmount()
    }).not.toThrow()
  })

  it('should handle multiple hook instances with same manager', () => {
    const { result: result1 } = renderHook(() => useSharedState(manager))
    const { result: result2 } = renderHook(() => useSharedState(manager))
    
    expect(result1.current.state.count).toBe(0)
    expect(result2.current.state.count).toBe(0)
    expect(result1.current.tabId).toBe(result2.current.tabId)
    
    act(() => {
      const state = manager.getState()
      state.count = 100
    })
    
    expect(result1.current.state.count).toBe(100)
    expect(result2.current.state.count).toBe(100)
  })

  it('should maintain referential stability for state snapshot', () => {
    const { result, rerender } = renderHook(() => useSharedState(manager))
    
    const state1 = result.current.state
    
    // Rerender without state change
    rerender()
    
    const state2 = result.current.state
    
    // Should be the same reference (Valtio optimization)
    expect(state1).toBe(state2)
    
    // Now change state
    act(() => {
      const state = manager.getState()
      state.count = 999
    })
    
    const state3 = result.current.state
    
    // Should be different reference after change
    expect(state1).not.toBe(state3)
    expect(state3.count).toBe(999)
  })
})
