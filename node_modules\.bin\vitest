#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/d/Projects/valtab/node_modules/.pnpm/vitest@1.6.1_@types+node@20.19.9_@vitest+ui@1.6.1_jsdom@23.2.0/node_modules/vitest/node_modules:/mnt/d/Projects/valtab/node_modules/.pnpm/vitest@1.6.1_@types+node@20.19.9_@vitest+ui@1.6.1_jsdom@23.2.0/node_modules:/mnt/d/Projects/valtab/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/d/Projects/valtab/node_modules/.pnpm/vitest@1.6.1_@types+node@20.19.9_@vitest+ui@1.6.1_jsdom@23.2.0/node_modules/vitest/node_modules:/mnt/d/Projects/valtab/node_modules/.pnpm/vitest@1.6.1_@types+node@20.19.9_@vitest+ui@1.6.1_jsdom@23.2.0/node_modules:/mnt/d/Projects/valtab/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../vitest/vitest.mjs" "$@"
else
  exec node  "$basedir/../vitest/vitest.mjs" "$@"
fi
