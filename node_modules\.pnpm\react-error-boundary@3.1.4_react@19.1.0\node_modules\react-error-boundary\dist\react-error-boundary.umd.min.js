!function(e,r){"object"==typeof exports&&"undefined"!=typeof module?r(exports,require("react")):"function"==typeof define&&define.amd?define(["exports","react"],r):r((e="undefined"!=typeof globalThis?globalThis:e||self).ReactErrorBoundary={},e.React)}(this,(function(e,r){"use strict";function t(e){if(e&&e.__esModule)return e;var r=Object.create(null);return e&&Object.keys(e).forEach((function(t){if("default"!==t){var n=Object.getOwnPropertyDescriptor(e,t);Object.defineProperty(r,t,n.get?n:{enumerable:!0,get:function(){return e[t]}})}})),r.default=e,Object.freeze(r)}var n=t(r);function o(e,r){return o=Object.setPrototypeOf||function(e,r){return e.__proto__=r,e},o(e,r)}var a={error:null},u=function(e){var r,t;function u(){for(var r,t=arguments.length,n=new Array(t),o=0;o<t;o++)n[o]=arguments[o];return(r=e.call.apply(e,[this].concat(n))||this).state=a,r.resetErrorBoundary=function(){for(var e,t=arguments.length,n=new Array(t),o=0;o<t;o++)n[o]=arguments[o];null==r.props.onReset||(e=r.props).onReset.apply(e,n),r.reset()},r}t=e,(r=u).prototype=Object.create(t.prototype),r.prototype.constructor=r,o(r,t),u.getDerivedStateFromError=function(e){return{error:e}};var i=u.prototype;return i.reset=function(){this.setState(a)},i.componentDidCatch=function(e,r){var t,n;null==(t=(n=this.props).onError)||t.call(n,e,r)},i.componentDidUpdate=function(e,r){var t,n,o,a,u=this.state.error,i=this.props.resetKeys;null!==u&&null!==r.error&&(void 0===(o=e.resetKeys)&&(o=[]),void 0===(a=i)&&(a=[]),o.length!==a.length||o.some((function(e,r){return!Object.is(e,a[r])})))&&(null==(t=(n=this.props).onResetKeysChange)||t.call(n,e.resetKeys,i),this.reset())},i.render=function(){var e=this.state.error,r=this.props,t=r.fallbackRender,o=r.FallbackComponent,a=r.fallback;if(null!==e){var u={error:e,resetErrorBoundary:this.resetErrorBoundary};if(n.isValidElement(a))return a;if("function"==typeof t)return t(u);if(o)return n.createElement(o,u);throw new Error("react-error-boundary requires either a fallback, fallbackRender, or FallbackComponent prop")}return this.props.children},u}(n.Component);e.ErrorBoundary=u,e.useErrorHandler=function(e){var r=n.useState(null),t=r[0],o=r[1];if(null!=e)throw e;if(null!=t)throw t;return o},e.withErrorBoundary=function(e,r){var t=function(t){return n.createElement(u,r,n.createElement(e,t))},o=e.displayName||e.name||"Unknown";return t.displayName="withErrorBoundary("+o+")",t},Object.defineProperty(e,"__esModule",{value:!0})}));
//# sourceMappingURL=react-error-boundary.umd.min.js.map
