{"name": "derive-valtio", "description": "Derive util for <PERSON><PERSON><PERSON>", "version": "0.1.0", "author": "<PERSON><PERSON>", "repository": {"type": "git", "url": "https://github.com/dai-shi/derive-valtio.git"}, "source": "./src/index.ts", "main": "./dist/index.umd.js", "module": "./dist/index.modern.js", "types": "./dist/src/index.d.ts", "exports": {"./package.json": "./package.json", ".": {"types": "./dist/src/index.d.ts", "module": "./dist/index.modern.js", "import": "./dist/index.modern.mjs", "default": "./dist/index.umd.js"}}, "sideEffects": false, "files": ["src", "dist"], "scripts": {"compile": "microbundle build -f modern,umd --globals react=React,valtio/vanilla=valtioVanilla", "postcompile": "cp dist/index.modern.mjs dist/index.modern.js && cp dist/index.modern.mjs.map dist/index.modern.js.map", "test": "run-s eslint tsc-test jest", "eslint": "eslint --ext .js,.ts,.tsx .", "jest": "jest", "tsc-test": "tsc --project . --noEmit", "examples:01_typescript": "DIR=01_typescript EXT=tsx webpack serve"}, "jest": {"testEnvironment": "jsdom", "preset": "ts-jest/presets/js-with-ts"}, "keywords": ["react", "derive", "valtio", "state"], "license": "MIT", "devDependencies": {"@testing-library/react": "^14.0.0", "@types/jest": "^29.5.6", "@types/node": "^20.8.7", "@types/react": "^18.2.31", "@types/react-dom": "^18.2.14", "@typescript-eslint/eslint-plugin": "^6.8.0", "@typescript-eslint/parser": "^6.8.0", "eslint": "^8.52.0", "eslint-config-prettier": "^9.0.0", "eslint-import-resolver-alias": "^1.1.2", "eslint-plugin-import": "^2.28.1", "eslint-plugin-jest": "^27.4.3", "eslint-plugin-prettier": "^5.0.1", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "html-webpack-plugin": "^5.5.3", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "microbundle": "^0.15.1", "npm-run-all": "^4.1.5", "prettier": "^3.0.3", "react": "^18.2.0", "react-dom": "^18.2.0", "react-error-boundary": "^4.0.11", "ts-jest": "^29.1.1", "ts-loader": "^9.5.0", "typescript": "^5.2.2", "valtio": "^1.11.2", "webpack": "^5.89.0", "webpack-cli": "^5.1.4", "webpack-dev-server": "^4.15.1"}, "peerDependencies": {"valtio": "*"}}