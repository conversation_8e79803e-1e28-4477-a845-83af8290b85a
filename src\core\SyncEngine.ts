import type { 
  SyncMessage, 
  ConflictResolutionStrategy, 
  StateWrapper, 
  SerializableState,
  EventListener,
  StateManagerEvent
} from '@/types'
import { generateTabId, deepMerge, shallowMerge, deepEqual } from '@/utils'

/**
 * Handles cross-tab communication and synchronization
 */
export class SyncEngine<T extends SerializableState> {
  private channel: BroadcastChannel
  private tabId: string
  private conflictResolution: ConflictResolutionStrategy
  private debug: boolean
  private connectedTabs = new Set<string>()
  private lastPingTime = 0
  private pingInterval: number | null = null
  private eventListeners = new Map<StateManagerEvent, Set<EventListener>>()

  constructor(
    channelName: string,
    conflictResolution: ConflictResolutionStrategy = 'last-write-wins',
    debug = false
  ) {
    this.tabId = generateTabId()
    this.conflictResolution = conflictResolution
    this.debug = debug
    
    this.channel = new BroadcastChannel(channelName)
    this.channel.addEventListener('message', this.handleMessage.bind(this))
    
    this.log('SyncEngine initialized with tabId:', this.tabId)
    this.startPingInterval()
    this.sendPing()
  }

  /**
   * Get the unique tab identifier
   */
  getTabId(): string {
    return this.tabId
  }

  /**
   * Get number of connected tabs
   */
  getConnectedTabsCount(): number {
    return this.connectedTabs.size
  }

  /**
   * Check if connected to other tabs
   */
  isConnected(): boolean {
    return this.connectedTabs.size > 0
  }

  /**
   * Broadcast state update to other tabs
   */
  broadcastUpdate(stateWrapper: StateWrapper<T>, path?: string[]): void {
    const message: SyncMessage = {
      type: 'state-update',
      tabId: this.tabId,
      timestamp: Date.now(),
      data: stateWrapper,
      path
    }
    
    this.channel.postMessage(message)
    this.log('Broadcasted state update:', message)
  }

  /**
   * Send ping to discover other tabs
   */
  sendPing(): void {
    const message: SyncMessage = {
      type: 'ping',
      tabId: this.tabId,
      timestamp: Date.now()
    }
    
    this.channel.postMessage(message)
    this.lastPingTime = Date.now()
    this.log('Sent ping')
  }

  /**
   * Resolve conflict between local and remote state
   */
  resolveConflict(
    localState: StateWrapper<T>, 
    remoteState: StateWrapper<T>
  ): StateWrapper<T> {
    this.log('Resolving conflict between states:', { localState, remoteState })

    if (typeof this.conflictResolution === 'function') {
      const resolved = this.conflictResolution(
        localState.data, 
        remoteState.data
      )
      return {
        data: resolved,
        metadata: {
          version: Math.max(localState.metadata.version, remoteState.metadata.version) + 1,
          lastModified: Date.now(),
          modifiedBy: this.tabId
        }
      }
    }

    switch (this.conflictResolution) {
      case 'last-write-wins':
        return localState.metadata.lastModified >= remoteState.metadata.lastModified
          ? localState
          : remoteState

      case 'merge-deep':
        return {
          data: deepMerge(localState.data, remoteState.data),
          metadata: {
            version: Math.max(localState.metadata.version, remoteState.metadata.version) + 1,
            lastModified: Date.now(),
            modifiedBy: this.tabId
          }
        }

      case 'merge-shallow':
        return {
          data: shallowMerge(localState.data, remoteState.data),
          metadata: {
            version: Math.max(localState.metadata.version, remoteState.metadata.version) + 1,
            lastModified: Date.now(),
            modifiedBy: this.tabId
          }
        }

      default:
        return remoteState
    }
  }

  /**
   * Add event listener
   */
  on(event: StateManagerEvent, listener: EventListener): void {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, new Set())
    }
    this.eventListeners.get(event)!.add(listener)
  }

  /**
   * Remove event listener
   */
  off(event: StateManagerEvent, listener: EventListener): void {
    const listeners = this.eventListeners.get(event)
    if (listeners) {
      listeners.delete(listener)
    }
  }

  /**
   * Cleanup resources
   */
  destroy(): void {
    if (this.pingInterval) {
      clearInterval(this.pingInterval)
      this.pingInterval = null
    }
    
    // Notify other tabs that this tab is closing
    const message: SyncMessage = {
      type: 'tab-closed',
      tabId: this.tabId,
      timestamp: Date.now()
    }
    this.channel.postMessage(message)
    
    this.channel.close()
    this.eventListeners.clear()
    this.log('SyncEngine destroyed')
  }

  private handleMessage(event: MessageEvent<SyncMessage>): void {
    const message = event.data
    
    if (message.tabId === this.tabId) {
      return // Ignore messages from self
    }

    this.log('Received message:', message)

    switch (message.type) {
      case 'ping':
        this.handlePing(message)
        break
      case 'pong':
        this.handlePong(message)
        break
      case 'state-update':
        this.handleStateUpdate(message)
        break
      case 'tab-closed':
        this.handleTabClosed(message)
        break
    }
  }

  private handlePing(message: SyncMessage): void {
    // Respond to ping
    const pong: SyncMessage = {
      type: 'pong',
      tabId: this.tabId,
      timestamp: Date.now()
    }
    this.channel.postMessage(pong)
    
    // Add tab to connected tabs
    if (!this.connectedTabs.has(message.tabId)) {
      this.connectedTabs.add(message.tabId)
      this.emit('tab-added', { tabId: message.tabId })
      this.emit('connected', { connectedTabs: this.connectedTabs.size })
    }
  }

  private handlePong(message: SyncMessage): void {
    if (!this.connectedTabs.has(message.tabId)) {
      this.connectedTabs.add(message.tabId)
      this.emit('tab-added', { tabId: message.tabId })
      this.emit('connected', { connectedTabs: this.connectedTabs.size })
    }
  }

  private handleStateUpdate(message: SyncMessage): void {
    this.emit('sync', {
      remoteState: message.data,
      fromTab: message.tabId,
      path: message.path
    })
  }

  private handleTabClosed(message: SyncMessage): void {
    if (this.connectedTabs.has(message.tabId)) {
      this.connectedTabs.delete(message.tabId)
      this.emit('tab-removed', { tabId: message.tabId })
      
      if (this.connectedTabs.size === 0) {
        this.emit('disconnected', { connectedTabs: 0 })
      }
    }
  }

  private startPingInterval(): void {
    this.pingInterval = window.setInterval(() => {
      this.sendPing()
      
      // Clean up stale connections (tabs that haven't responded in 30 seconds)
      const now = Date.now()
      const staleThreshold = 30000 // 30 seconds
      
      if (now - this.lastPingTime > staleThreshold) {
        const staleTabs = Array.from(this.connectedTabs)
        staleTabs.forEach(tabId => {
          this.connectedTabs.delete(tabId)
          this.emit('tab-removed', { tabId })
        })
        
        if (this.connectedTabs.size === 0) {
          this.emit('disconnected', { connectedTabs: 0 })
        }
      }
    }, 5000) // Ping every 5 seconds
  }

  private emit(event: StateManagerEvent, data?: any): void {
    const listeners = this.eventListeners.get(event)
    if (listeners) {
      listeners.forEach(listener => {
        try {
          listener(data)
        } catch (error) {
          console.error(`Error in event listener for ${event}:`, error)
        }
      })
    }
  }

  private log(...args: any[]): void {
    if (this.debug) {
      console.log(`[SyncEngine:${this.tabId.slice(-8)}]`, ...args)
    }
  }
}
