import { describe, it, expect } from 'vitest'
import {
  generateTabId,
  generateShortId,
  generateVersion,
  deepMerge,
  shallowMerge,
  deepClone,
  deepEqual,
} from '../utils'

describe('generateId', () => {
  it('should generate unique tab IDs', () => {
    const id1 = generateTabId()
    const id2 = generateTabId()

    expect(id1).toBeTruthy()
    expect(id2).toBeTruthy()
    expect(id1).not.toBe(id2)
  })

  it('should generate short IDs', () => {
    const id = generateShortId()
    expect(id).toBeTruthy()
    expect(typeof id).toBe('string')
    expect(id.length).toBeGreaterThan(0)
  })

  it('should generate version numbers', () => {
    const version1 = generateVersion()
    const version2 = generateVersion()

    expect(typeof version1).toBe('number')
    expect(typeof version2).toBe('number')
    expect(version2).toBeGreaterThanOrEqual(version1)
  })
})

describe('deepMerge', () => {
  it('should merge simple objects', () => {
    const target = { a: 1, b: 2 }
    const source = { b: 3, c: 4 }
    const result = deepMerge(target, source)

    expect(result).toEqual({ a: 1, b: 3, c: 4 })
  })

  it('should merge nested objects', () => {
    const target = {
      user: { name: 'John', age: 30 },
      settings: { theme: 'dark' },
    }
    const source = {
      user: { age: 31 },
      settings: { language: 'en' },
    } as any
    const result = deepMerge(target, source)

    expect(result).toEqual({
      user: { name: 'John', age: 31 },
      settings: { theme: 'dark', language: 'en' },
    })
  })

  it('should replace arrays instead of merging', () => {
    const target = { items: [1, 2, 3] }
    const source = { items: [4, 5] }
    const result = deepMerge(target, source)

    expect(result).toEqual({ items: [4, 5] })
  })

  it('should not mutate original objects', () => {
    const target = { a: 1 }
    const source = { b: 2 } as any
    const result = deepMerge(target, source)

    expect(target).toEqual({ a: 1 })
    expect(source).toEqual({ b: 2 })
    expect(result).toEqual({ a: 1, b: 2 })
  })
})

describe('shallowMerge', () => {
  it('should merge objects shallowly', () => {
    const target = { a: 1, b: { x: 1 } }
    const source = { b: { y: 2 }, c: 3 } as any
    const result = shallowMerge(target, source)

    expect(result).toEqual({ a: 1, b: { y: 2 }, c: 3 })
  })
})

describe('deepClone', () => {
  it('should clone simple objects', () => {
    const obj = { a: 1, b: 'test', c: true }
    const cloned = deepClone(obj)

    expect(cloned).toEqual(obj)
    expect(cloned).not.toBe(obj)
  })

  it('should clone nested objects', () => {
    const obj = {
      user: { name: 'John', settings: { theme: 'dark' } },
      items: [1, 2, { id: 3 }],
    }
    const cloned = deepClone(obj)

    expect(cloned).toEqual(obj)
    expect(cloned).not.toBe(obj)
    expect(cloned.user).not.toBe(obj.user)
    expect(cloned.items).not.toBe(obj.items)
  })

  it('should handle null and primitives', () => {
    expect(deepClone(null)).toBe(null)
    expect(deepClone(42)).toBe(42)
    expect(deepClone('test')).toBe('test')
    expect(deepClone(true)).toBe(true)
  })
})

describe('deepEqual', () => {
  it('should compare primitive values', () => {
    expect(deepEqual(1, 1)).toBe(true)
    expect(deepEqual('test', 'test')).toBe(true)
    expect(deepEqual(true, true)).toBe(true)
    expect(deepEqual(null, null)).toBe(true)
    expect(deepEqual(undefined, undefined)).toBe(true)

    expect(deepEqual(1, 2)).toBe(false)
    expect(deepEqual('test', 'other')).toBe(false)
    expect(deepEqual(true, false)).toBe(false)
    expect(deepEqual(null, undefined)).toBe(false)
  })

  it('should compare objects', () => {
    expect(deepEqual({ a: 1 }, { a: 1 })).toBe(true)
    expect(deepEqual({ a: 1, b: 2 }, { b: 2, a: 1 })).toBe(true)

    expect(deepEqual({ a: 1 }, { a: 2 })).toBe(false)
    expect(deepEqual({ a: 1 }, { a: 1, b: 2 })).toBe(false)
  })

  it('should compare arrays', () => {
    expect(deepEqual([1, 2, 3], [1, 2, 3])).toBe(true)
    expect(deepEqual([], [])).toBe(true)

    expect(deepEqual([1, 2, 3], [1, 2, 4])).toBe(false)
    expect(deepEqual([1, 2], [1, 2, 3])).toBe(false)
  })

  it('should compare nested structures', () => {
    const obj1 = {
      user: { name: 'John', items: [1, 2] },
      settings: { theme: 'dark' },
    }
    const obj2 = {
      user: { name: 'John', items: [1, 2] },
      settings: { theme: 'dark' },
    }
    const obj3 = {
      user: { name: 'Jane', items: [1, 2] },
      settings: { theme: 'dark' },
    }

    expect(deepEqual(obj1, obj2)).toBe(true)
    expect(deepEqual(obj1, obj3)).toBe(false)
  })
})
