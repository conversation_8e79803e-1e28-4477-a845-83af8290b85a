{"version": 3, "file": "react-error-boundary.umd.min.js", "sources": ["../node_modules/@babel/runtime/helpers/esm/setPrototypeOf.js", "../src/index.tsx", "../node_modules/@babel/runtime/helpers/esm/inheritsLoose.js"], "sourcesContent": ["export default function _setPrototypeOf(o, p) {\n  _setPrototypeOf = Object.setPrototypeOf || function _setPrototypeOf(o, p) {\n    o.__proto__ = p;\n    return o;\n  };\n\n  return _setPrototypeOf(o, p);\n}", "import * as React from 'react'\n\nconst changedArray = (a: Array<unknown> = [], b: Array<unknown> = []) =>\n  a.length !== b.length || a.some((item, index) => !Object.is(item, b[index]))\n\ninterface FallbackProps {\n  error: Error\n  resetErrorBoundary: (...args: Array<unknown>) => void\n}\n\ninterface ErrorBoundaryPropsWithComponent {\n  onResetKeysChange?: (\n    prevResetKeys: Array<unknown> | undefined,\n    resetKeys: Array<unknown> | undefined,\n  ) => void\n  onReset?: (...args: Array<unknown>) => void\n  onError?: (error: Error, info: {componentStack: string}) => void\n  resetKeys?: Array<unknown>\n  fallback?: never\n  FallbackComponent: React.ComponentType<FallbackProps>\n  fallbackRender?: never\n}\n\ndeclare function FallbackRender(\n  props: FallbackProps,\n): React.ReactElement<\n  unknown,\n  string | React.FunctionComponent | typeof React.Component\n> | null\n\ninterface ErrorBoundaryPropsWithRender {\n  onResetKeysChange?: (\n    prevResetKeys: Array<unknown> | undefined,\n    resetKeys: Array<unknown> | undefined,\n  ) => void\n  onReset?: (...args: Array<unknown>) => void\n  onError?: (error: Error, info: {componentStack: string}) => void\n  resetKeys?: Array<unknown>\n  fallback?: never\n  FallbackComponent?: never\n  fallbackRender: typeof FallbackRender\n}\n\ninterface ErrorBoundaryPropsWithFallback {\n  onResetKeysChange?: (\n    prevResetKeys: Array<unknown> | undefined,\n    resetKeys: Array<unknown> | undefined,\n  ) => void\n  onReset?: (...args: Array<unknown>) => void\n  onError?: (error: Error, info: {componentStack: string}) => void\n  resetKeys?: Array<unknown>\n  fallback: React.ReactElement<\n    unknown,\n    string | React.FunctionComponent | typeof React.Component\n  > | null\n  FallbackComponent?: never\n  fallbackRender?: never\n}\n\ntype ErrorBoundaryProps =\n  | ErrorBoundaryPropsWithFallback\n  | ErrorBoundaryPropsWithComponent\n  | ErrorBoundaryPropsWithRender\n\ntype ErrorBoundaryState = {error: Error | null}\n\nconst initialState: ErrorBoundaryState = {error: null}\n\nclass ErrorBoundary extends React.Component<\n  React.PropsWithRef<React.PropsWithChildren<ErrorBoundaryProps>>,\n  ErrorBoundaryState\n> {\n  static getDerivedStateFromError(error: Error) {\n    return {error}\n  }\n\n  state = initialState\n  resetErrorBoundary = (...args: Array<unknown>) => {\n    this.props.onReset?.(...args)\n    this.reset()\n  }\n\n  reset() {\n    this.setState(initialState)\n  }\n\n  componentDidCatch(error: Error, info: React.ErrorInfo) {\n    this.props.onError?.(error, info)\n  }\n\n  componentDidUpdate(\n    prevProps: ErrorBoundaryProps,\n    prevState: ErrorBoundaryState,\n  ) {\n    const {error} = this.state\n    const {resetKeys} = this.props\n\n    // There's an edge case where if the thing that triggered the error\n    // happens to *also* be in the resetKeys array, we'd end up resetting\n    // the error boundary immediately. This would likely trigger a second\n    // error to be thrown.\n    // So we make sure that we don't check the resetKeys on the first call\n    // of cDU after the error is set\n\n    if (\n      error !== null &&\n      prevState.error !== null &&\n      changedArray(prevProps.resetKeys, resetKeys)\n    ) {\n      this.props.onResetKeysChange?.(prevProps.resetKeys, resetKeys)\n      this.reset()\n    }\n  }\n\n  render() {\n    const {error} = this.state\n\n    const {fallbackRender, FallbackComponent, fallback} = this.props\n\n    if (error !== null) {\n      const props = {\n        error,\n        resetErrorBoundary: this.resetErrorBoundary,\n      }\n      if (React.isValidElement(fallback)) {\n        return fallback\n      } else if (typeof fallbackRender === 'function') {\n        return fallbackRender(props)\n      } else if (FallbackComponent) {\n        return <FallbackComponent {...props} />\n      } else {\n        throw new Error(\n          'react-error-boundary requires either a fallback, fallbackRender, or FallbackComponent prop',\n        )\n      }\n    }\n\n    return this.props.children\n  }\n}\n\nfunction withErrorBoundary<P>(\n  Component: React.ComponentType<P>,\n  errorBoundaryProps: ErrorBoundaryProps,\n): React.ComponentType<P> {\n  const Wrapped: React.ComponentType<P> = props => {\n    return (\n      <ErrorBoundary {...errorBoundaryProps}>\n        <Component {...props} />\n      </ErrorBoundary>\n    )\n  }\n\n  // Format for display in DevTools\n  const name = Component.displayName || Component.name || 'Unknown'\n  Wrapped.displayName = `withErrorBoundary(${name})`\n\n  return Wrapped\n}\n\nfunction useErrorHandler(givenError?: unknown): (error: unknown) => void {\n  const [error, setError] = React.useState<unknown>(null)\n  if (givenError != null) throw givenError\n  if (error != null) throw error\n  return setError\n}\n\nexport {ErrorBoundary, withErrorBoundary, useErrorHandler}\nexport type {\n  FallbackProps,\n  ErrorBoundaryPropsWithComponent,\n  ErrorBoundaryPropsWithRender,\n  ErrorBoundaryPropsWithFallback,\n  ErrorBoundaryProps,\n}\n\n/*\neslint\n  @typescript-eslint/sort-type-union-intersection-members: \"off\",\n  @typescript-eslint/no-throw-literal: \"off\",\n  @typescript-eslint/prefer-nullish-coalescing: \"off\"\n*/\n", "import setPrototypeOf from \"./setPrototypeOf.js\";\nexport default function _inheritsLoose(subClass, superClass) {\n  subClass.prototype = Object.create(superClass.prototype);\n  subClass.prototype.constructor = subClass;\n  setPrototypeOf(subClass, superClass);\n}"], "names": ["_setPrototypeOf", "o", "p", "Object", "setPrototypeOf", "__proto__", "initialState", "error", "Error<PERSON>ou<PERSON><PERSON>", "subClass", "superClass", "state", "resetErrorBoundary", "args", "props", "onReset", "reset", "prototype", "create", "constructor", "getDerivedStateFromError", "setState", "componentDidCatch", "info", "onError", "componentDidUpdate", "prevProps", "prevState", "a", "b", "this", "resetKeys", "length", "some", "item", "index", "is", "onResetKeysChange", "render", "fallback<PERSON><PERSON>", "FallbackComponent", "fallback", "React", "isValidElement", "Error", "children", "Component", "given<PERSON><PERSON><PERSON>", "useState", "setError", "errorBoundaryProps", "Wrapped", "name", "displayName"], "mappings": "ukBAAe,SAASA,EAAgBC,EAAGC,UACzCF,EAAkBG,OAAOC,gBAAkB,SAAyBH,EAAGC,UACrED,EAAEI,UAAYH,EACPD,GAGFD,EAAgBC,EAAGC,GCJ5B,IAgEMI,EAAmC,CAACC,MAAO,MAE3CC,cCnES,IAAwBC,EAAUC,0ID2E/CC,MAAQL,IACRM,mBAAqB,wCAAIC,2BAAAA,0BAClBC,MAAMC,cAAND,OAAMC,gBAAaF,KACnBG,WC9EwCN,KAAVD,KAC5BQ,UAAYd,OAAOe,OAAOR,EAAWO,WAC9CR,EAASQ,UAAUE,YAAcV,EACjCL,EAAeK,EAAUC,KDoElBU,yBAAP,SAAgCb,SACvB,CAACA,MAAAA,+BASVS,MAAA,gBACOK,SAASf,MAGhBgB,kBAAA,SAAkBf,EAAcgB,4BACzBT,OAAMU,mBAAUjB,EAAOgB,MAG9BE,mBAAA,SACEC,EACAC,WA1FkBC,EAAwBC,EA4FnCtB,EAASuB,KAAKnB,MAAdJ,MACAwB,EAAaD,KAAKhB,MAAlBiB,UAUK,OAAVxB,GACoB,OAApBoB,EAAUpB,kBAxGMqB,EAyGHF,EAAUK,aAzGPH,EAAoB,cAAIC,EAyGNE,KAzGMF,EAAoB,IAChED,EAAEI,SAAWH,EAAEG,QAAUJ,EAAEK,MAAK,SAACC,EAAMC,UAAWhC,OAAOiC,GAAGF,EAAML,EAAEM,2BA0G3DrB,OAAMuB,6BAAoBX,EAAUK,UAAWA,QAC/Cf,YAITsB,OAAA,eACS/B,EAASuB,KAAKnB,MAAdJ,QAE+CuB,KAAKhB,MAApDyB,IAAAA,eAAgBC,IAAAA,kBAAmBC,IAAAA,YAE5B,OAAVlC,EAAgB,KACZO,EAAQ,CACZP,MAAAA,EACAK,mBAAoBkB,KAAKlB,uBAEvB8B,EAAMC,eAAeF,UAChBA,EACF,GAA8B,mBAAnBF,SACTA,EAAezB,GACjB,GAAI0B,SACFE,gBAACF,EAAsB1B,SAExB,IAAI8B,MACR,qGAKCd,KAAKhB,MAAM+B,aArEMH,EAAMI,+CA4FlC,SAAyBC,SACGL,EAAMM,SAAkB,MAA3CzC,OAAO0C,UACI,MAAdF,EAAoB,MAAMA,KACjB,MAATxC,EAAe,MAAMA,SAClB0C,uBAvBT,SACEH,EACAI,OAEMC,EAAkC,SAAArC,UAEpC4B,gBAAClC,EAAkB0C,EACjBR,gBAACI,EAAchC,KAMfsC,EAAON,EAAUO,aAAeP,EAAUM,MAAQ,iBACxDD,EAAQE,iCAAmCD,MAEpCD"}