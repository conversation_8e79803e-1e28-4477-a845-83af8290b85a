!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports,require("@babel/helper-module-imports"),require("@babel/types"),require("aslemammad-vite-plugin-macro"),require("babel-plugin-macros")):"function"==typeof define&&define.amd?define(["exports","@babel/helper-module-imports","@babel/types","aslemammad-vite-plugin-macro","babel-plugin-macros"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).valtioMacroVite={},e.babelModuleImports,e.t,e.plugin,e.babelMacro)}(this,(function(e,t,r,a,o){"use strict";function n(e){var t=Object.create(null);return e&&Object.keys(e).forEach((function(r){if("default"!==r){var a=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(t,r,a.get?a:{enumerable:!0,get:function(){return e[r]}})}})),t.default=e,Object.freeze(t)}var i=n(t),l=n(r),u=n(a),c=n(o),d="default"in u?u.default:u,f=d.defineMacro,s=d.defineMacroProvider,p=d.createMacroPlugin,b=f("useProxy").withSignature("<T extends object>(proxyObject: T): void").withHandler((function(e){var t,r,a,o=e.path,n=e.args,u=i.addNamed(o,"useSnapshot","valtio"),d=null==(t=n[0])?void 0:t.node;if(!l.isIdentifier(d))throw new c.MacroError("no proxy object");var f=l.identifier("valtio_macro_snap_"+d.name);null==(r=o.parentPath)||r.replaceWith(l.variableDeclaration("const",[l.variableDeclarator(f,l.callExpression(u,[d]))]));var s=0;null==(a=o.parentPath)||null==(a=a.getFunctionParent())||a.traverse({Identifier:function(e){0===s&&e.node!==d&&e.node.name===d.name&&(e.node.name=f.name)},Function:{enter:function(){++s},exit:function(){--s}}})}));function m(){return s({id:"valtio/macro",exports:{"valtio/macro":{macros:[b]}}})}var v=p({}).use(m());e.default=v,e.provideValtioMacro=m,e.valtioMacro=b,Object.defineProperty(e,"__esModule",{value:!0})}));
