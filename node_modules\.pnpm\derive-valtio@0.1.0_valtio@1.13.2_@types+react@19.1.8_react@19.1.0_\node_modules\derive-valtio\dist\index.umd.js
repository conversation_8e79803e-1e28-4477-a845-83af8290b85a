!function(e,n){"object"==typeof exports&&"undefined"!=typeof module?n(exports,require("valtio/vanilla")):"function"==typeof define&&define.amd?define(["exports","valtio/vanilla"],n):n((e||self).deriveValtio={},e.valtio<PERSON>lla)}(this,function(e,n){var r=new WeakMap,t=new WeakMap,i=function e(n,t){var i=r.get(n);i&&(i[0].forEach(function(r){var t=r.d;n!==t&&e(t)}),++i[2],t&&i[3].add(t))},o=function e(n){var t=r.get(n);t&&(--t[2],t[2]||(t[3].forEach(function(e){return e()}),t[3].clear()),t[0].forEach(function(r){var t=r.d;n!==t&&e(t)}))},a=function(e){var a=e.s,f=t.get(e.d);f||(f=[new Set],t.set(e.d,f)),f[0].add(e);var u=r.get(a);if(!u){var l=new Set,c=n.subscribe(a,function(e){l.forEach(function(n){var r=n.c,t=n.n,f=n.i;a===n.d&&e.every(function(e){return 1===e[1].length&&f.includes(e[1][0])})||n.p||(i(a,r),t?o(a):n.p=Promise.resolve().then(function(){delete n.p,o(a)}))})},!0);u=[l,c,0,new Set],r.set(a,u)}u[0].add(e)},f=function(e){var n=e.s,i=e.d,o=t.get(i);null==o||o[0].delete(e),0===(null==o?void 0:o[0].size)&&t.delete(i);var a=r.get(n);if(a){var f=a[0],u=a[1];f.delete(e),f.size||(u(),r.delete(n))}},u=function(e){var n=t.get(e);return n?Array.from(n[0]):[]},l={add:a,remove:f,list:u};e.derive=function(e,t){var i=(null==t?void 0:t.proxy)||n.proxy({}),o=!(null==t||!t.sync),u=Object.keys(e);return u.forEach(function(t){if(Object.getOwnPropertyDescriptor(i,t))throw new Error("object property already defined");var l=e[t],c=null;!function e(){if(c){if(Array.from(c).map(function(n){var t,i;return t=e,!(null==(i=r.get(n[0]))||!i[2]||(i[3].add(t),0))}).some(function(e){return e}))return;if(Array.from(c).every(function(e){var r=e[1];return n.getVersion(e[0])===r.v}))return}var d=new Map,s=l(function(e){return d.set(e,{v:n.getVersion(e)}),e}),v=function(){var n;d.forEach(function(n,r){var f,l=null==(f=c)||null==(f=f.get(r))?void 0:f.s;if(l)n.s=l;else{var d={s:r,d:i,k:t,c:e,n:o,i:u};a(d),n.s=d}}),null==(n=c)||n.forEach(function(e,n){!d.has(n)&&e.s&&f(e.s)}),c=d};s instanceof Promise?s.finally(v):v(),i[t]=s}()}),i},e.underive=function(e,n){var r=null!=n&&n.delete?new Set:null;u(e).forEach(function(e){var t=e.k;null!=n&&n.keys&&!n.keys.includes(t)||(f(e),r&&r.add(t))}),r&&r.forEach(function(n){delete e[n]})},e.unstable_deriveSubscriptions=l});
//# sourceMappingURL=index.umd.js.map
